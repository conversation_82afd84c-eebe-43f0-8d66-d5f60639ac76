{"name": "youtube-studio-analytics-extension", "version": "1.0.0", "description": "Chrome extension for YouTube Studio analytics and optimal posting times", "main": "background.js", "scripts": {"build": "echo 'No build process needed for this extension'", "test": "echo 'No tests configured yet'", "lint": "echo '<PERSON><PERSON> not configured yet'", "dev": "echo 'Load extension in Chrome developer mode'"}, "keywords": ["youtube", "analytics", "chrome-extension", "posting-times", "audience-insights"], "author": "Your Name", "license": "PROPRIETARY", "devDependencies": {"@types/chrome": "^0.0.246"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/youtube-studio-analytics-extension.git"}, "bugs": {"url": "https://github.com/yourusername/youtube-studio-analytics-extension/issues"}, "homepage": "https://github.com/yourusername/youtube-studio-analytics-extension#readme"}