/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    width: 400px;
    min-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
}

/* Header */
.header {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s;
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Authentication Section */
.auth-section {
    padding: 40px 20px;
    text-align: center;
}

.auth-content h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 22px;
}

.auth-content p {
    color: #666;
    margin-bottom: 30px;
    font-size: 14px;
}

.auth-btn {
    background: #4285f4;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    transition: background 0.2s;
}

.auth-btn:hover {
    background: #3367d6;
}

/* Dashboard */
.dashboard {
    padding: 20px;
    max-height: 540px;
    overflow-y: auto;
}

.dashboard::-webkit-scrollbar {
    width: 6px;
}

.dashboard::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.dashboard::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

/* Channel Info */
.channel-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.channel-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.channel-details h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.channel-details p {
    font-size: 12px;
    color: #666;
}

/* Sections */
.section {
    margin-bottom: 25px;
}

.section h2 {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Best Times */
.best-times {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.time-slot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.time-slot:last-child {
    border-bottom: none;
}

.time-info {
    display: flex;
    flex-direction: column;
}

.time-day {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.time-hour {
    font-size: 12px;
    color: #666;
}

.engagement-score {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* Weekly Schedule Styles */
.schedule-header {
    text-align: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e8eaed;
}

.schedule-header h3 {
    margin: 0 0 4px 0;
    color: #1a73e8;
    font-size: 16px;
    font-weight: 600;
}

.schedule-subtitle {
    margin: 0;
    color: #5f6368;
    font-size: 12px;
}

.schedule-day {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px 16px;
    border-left: 4px solid #4285f4;
    transition: all 0.2s ease;
}

.schedule-day:hover {
    background: #e8f0fe;
    transform: translateX(2px);
}

.day-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.day-name {
    font-weight: 600;
    color: #1a73e8;
    font-size: 14px;
    min-width: 80px;
}

.time-range {
    color: #202124;
    font-weight: 500;
    font-size: 13px;
    flex-grow: 1;
    text-align: center;
}

.confidence {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 60px;
}

.confidence-score {
    background: #34a853;
    color: white;
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.confidence-label {
    color: #5f6368;
    font-size: 9px;
    margin-top: 2px;
}

.day-reasoning {
    color: #5f6368;
    font-size: 11px;
    font-style: italic;
    margin-top: 4px;
}

.schedule-summary {
    background: #e8f0fe;
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 16px;
    border: 1px solid #dadce0;
}

.summary-item {
    color: #202124;
    font-size: 12px;
    margin-bottom: 4px;
}

.summary-item:last-child {
    margin-bottom: 0;
}

/* Heatmap */
.heatmap-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.heatmap {
    display: grid;
    grid-template-columns: repeat(24, 1fr);
    gap: 2px;
    margin-top: 10px;
}

.heatmap-cell {
    aspect-ratio: 1;
    border-radius: 2px;
    cursor: pointer;
    transition: transform 0.2s;
}

.heatmap-cell:hover {
    transform: scale(1.1);
}

.heatmap-legend {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 11px;
    color: #666;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.stat-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Settings */
.settings {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.setting-item {
    margin-bottom: 12px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    font-size: 13px;
    color: #333;
    margin-bottom: 5px;
}

.setting-item select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
}

/* Loading and Error States */
.loading {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: #ffebee;
    border: 1px solid #f44336;
    color: #c62828;
    padding: 15px;
    border-radius: 8px;
    margin: 20px;
    text-align: center;
}

.retry-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    font-size: 13px;
}

.retry-btn:hover {
    background: #d32f2f;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}
