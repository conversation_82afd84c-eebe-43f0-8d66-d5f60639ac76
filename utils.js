/**
 * Utility functions for YouTube Studio Analytics Extension
 */

class Utils {
    /**
     * Format numbers with appropriate suffixes (K, M, B)
     */
    static formatNumber(num) {
        if (num >= 1000000000) {
            return (num / 1000000000).toFixed(1) + 'B';
        }
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * Format time in 12-hour format
     */
    static formatTime(hour) {
        const period = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        return `${displayHour}:00 ${period}`;
    }

    /**
     * Get day name from day index
     */
    static getDayName(dayIndex) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return days[dayIndex];
    }

    /**
     * Get short day name from day index
     */
    static getShortDayName(dayIndex) {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[dayIndex];
    }

    /**
     * Calculate engagement score based on views and watch time
     */
    static calculateEngagementScore(views, watchTime, avgViews, avgWatchTime) {
        const viewScore = (views / avgViews) * 50;
        const watchTimeScore = (watchTime / avgWatchTime) * 50;
        return Math.min(100, Math.round(viewScore + watchTimeScore));
    }

    /**
     * Generate color for heatmap based on intensity
     */
    static getHeatmapColor(intensity) {
        // intensity should be between 0 and 1
        const colors = [
            '#ebedf0', // Very low
            '#c6e48b', // Low
            '#7bc96f', // Medium
            '#239a3b', // High
            '#196127'  // Very high
        ];
        
        const index = Math.floor(intensity * (colors.length - 1));
        return colors[Math.min(index, colors.length - 1)];
    }

    /**
     * Debounce function to limit API calls
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Show loading state
     */
    static showLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('hidden');
        }
    }

    /**
     * Hide loading state
     */
    static hideLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.add('hidden');
        }
    }

    /**
     * Show error message
     */
    static showError(message, elementId = 'errorMessage') {
        const errorElement = document.getElementById(elementId);
        const errorText = document.getElementById('errorText');
        
        if (errorElement && errorText) {
            errorText.textContent = message;
            errorElement.classList.remove('hidden');
        }
    }

    /**
     * Hide error message
     */
    static hideError(elementId = 'errorMessage') {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.add('hidden');
        }
    }

    /**
     * Get user's timezone
     */
    static getUserTimezone() {
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch (e) {
            return 'UTC';
        }
    }

    /**
     * Convert UTC time to user's timezone
     */
    static convertToUserTimezone(utcTime, timezone = null) {
        const userTimezone = timezone || this.getUserTimezone();
        const date = new Date(utcTime);
        return new Intl.DateTimeFormat('en-US', {
            timeZone: userTimezone,
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        }).format(date);
    }

    /**
     * Store data in Chrome storage
     */
    static async storeData(key, data) {
        try {
            await chrome.storage.local.set({ [key]: data });
            return true;
        } catch (error) {
            console.error('Error storing data:', error);
            return false;
        }
    }

    /**
     * Retrieve data from Chrome storage
     */
    static async getData(key) {
        try {
            const result = await chrome.storage.local.get([key]);
            return result[key] || null;
        } catch (error) {
            console.error('Error retrieving data:', error);
            return null;
        }
    }

    /**
     * Clear specific data from Chrome storage
     */
    static async clearData(key) {
        try {
            await chrome.storage.local.remove([key]);
            return true;
        } catch (error) {
            console.error('Error clearing data:', error);
            return false;
        }
    }

    /**
     * Validate API response
     */
    static validateApiResponse(response) {
        return response && 
               response.kind && 
               response.kind.includes('youtube') && 
               !response.error;
    }

    /**
     * Handle API errors
     */
    static handleApiError(error) {
        console.error('API Error:', error);
        
        if (error.status === 401) {
            return 'Authentication failed. Please sign in again.';
        } else if (error.status === 403) {
            return 'Access denied. Please check your permissions.';
        } else if (error.status === 429) {
            return 'Rate limit exceeded. Please try again later.';
        } else if (error.status >= 500) {
            return 'Server error. Please try again later.';
        } else {
            return 'An unexpected error occurred. Please try again.';
        }
    }

    /**
     * Generate date range for analytics
     */
    static getDateRange(days = 30) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - days);
        
        return {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
        };
    }

    /**
     * Parse YouTube Analytics data
     */
    static parseAnalyticsData(data) {
        if (!data || !data.rows) {
            return null;
        }

        const parsed = {
            totalViews: 0,
            totalWatchTime: 0,
            averageViewDuration: 0,
            hourlyData: new Array(24).fill(0),
            dailyData: new Array(7).fill(0)
        };

        data.rows.forEach(row => {
            // Process based on the metrics requested
            if (data.columnHeaders) {
                data.columnHeaders.forEach((header, index) => {
                    const value = row[index];
                    switch (header.name) {
                        case 'views':
                            parsed.totalViews += parseInt(value) || 0;
                            break;
                        case 'estimatedMinutesWatched':
                            parsed.totalWatchTime += parseInt(value) || 0;
                            break;
                        case 'averageViewDuration':
                            parsed.averageViewDuration = parseInt(value) || 0;
                            break;
                    }
                });
            }
        });

        return parsed;
    }

    /**
     * Create notification
     */
    static async createNotification(title, message, iconUrl = 'icons/icon48.png') {
        try {
            await chrome.notifications.create({
                type: 'basic',
                iconUrl: iconUrl,
                title: title,
                message: message
            });
        } catch (error) {
            console.error('Error creating notification:', error);
        }
    }

    /**
     * Log analytics event
     */
    static logEvent(eventName, eventData = {}) {
        console.log(`Analytics Event: ${eventName}`, eventData);
        // In a production environment, you might want to send this to an analytics service
    }
}
