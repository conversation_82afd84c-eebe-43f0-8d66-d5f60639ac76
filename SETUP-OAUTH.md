# OAuth Setup Guide - YouTube Studio Analytics Extension

## 🚀 Quick Setup Steps

### Step 1: Get Your Extension ID
1. Load the extension in Chrome (`chrome://extensions/`)
2. Enable "Developer mode"
3. Click "Load unpacked" and select this folder
4. **Copy the Extension ID** (it looks like: `abcdefghijklmnopqrstuvwxyz123456`)

### Step 2: Google Cloud Console Setup

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create a new project** or select existing one
3. **Enable APIs**:
   - Go to "APIs & Services" → "Library"
   - Search and enable: **YouTube Analytics API**
   - Search and enable: **YouTube Data API v3**

4. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: **Chrome Extension**
   - Name: `YouTube Studio Analytics Extension`
   - Application ID: **Paste your Extension ID from Step 1**

5. **Configure OAuth Consent Screen**:
   - Go to "APIs & Services" → "OAuth consent screen"
   - User Type: **External** (unless you have Google Workspace)
   - Fill in required fields:
     - App name: `YouTube Studio Analytics`
     - User support email: Your email
     - Developer contact: Your email
   - Add scopes:
     - `https://www.googleapis.com/auth/youtube.readonly`
     - `https://www.googleapis.com/auth/yt-analytics.readonly`

### Step 3: Update Extension Configuration

1. **Update manifest.json**:
   ```json
   {
     "oauth2": {
       "client_id": "YOUR_ACTUAL_CLIENT_ID.apps.googleusercontent.com",
       "scopes": [
         "https://www.googleapis.com/auth/youtube.readonly",
         "https://www.googleapis.com/auth/yt-analytics.readonly"
       ]
     }
   }
   ```

2. **Replace `YOUR_ACTUAL_CLIENT_ID`** with the Client ID from Google Cloud Console

### Step 4: Test the Extension

1. **Reload the extension** in Chrome
2. **Click the extension icon**
3. **Click "Sign in with Google"**
4. **Authorize the permissions**
5. **Check for any errors** in the browser console

## 🔧 Troubleshooting

### Error: "Please configure your Google Client ID"
- Make sure you updated the `client_id` in `manifest.json`
- The Client ID should end with `.apps.googleusercontent.com`

### Error: "redirect_uri_mismatch"
- Make sure you used the correct Extension ID in Google Cloud Console
- The Extension ID in Google Cloud must match the one shown in Chrome

### Error: "access_denied"
- Make sure you configured the OAuth consent screen
- Add the required scopes to your OAuth consent screen
- Try signing in with a different Google account

### Error: "invalid_client"
- Double-check your Client ID is correct
- Make sure the OAuth 2.0 client is configured for Chrome Extension

## 📋 Quick Checklist

- [ ] Extension loaded in Chrome developer mode
- [ ] Extension ID copied
- [ ] Google Cloud project created
- [ ] YouTube Analytics API enabled
- [ ] YouTube Data API v3 enabled
- [ ] OAuth 2.0 credentials created for Chrome Extension
- [ ] Extension ID added to OAuth client
- [ ] OAuth consent screen configured
- [ ] Required scopes added
- [ ] Client ID updated in manifest.json
- [ ] Extension reloaded in Chrome

## 🎯 Example Configuration

Your `manifest.json` should look like this:

```json
{
  "manifest_version": 3,
  "name": "YouTube Studio Analytics - Optimal Posting Times",
  "version": "1.0.0",
  "description": "Analyze your YouTube audience data to find the best times to post videos for maximum engagement",
  
  "permissions": [
    "identity",
    "storage",
    "notifications",
    "activeTab",
    "alarms"
  ],
  
  "host_permissions": [
    "https://www.googleapis.com/*",
    "https://studio.youtube.com/*"
  ],
  
  "oauth2": {
    "client_id": "123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com",
    "scopes": [
      "https://www.googleapis.com/auth/youtube.readonly",
      "https://www.googleapis.com/auth/yt-analytics.readonly"
    ]
  },
  
  "background": {
    "service_worker": "background.js"
  },
  
  "action": {
    "default_popup": "popup.html",
    "default_title": "YouTube Analytics - Best Posting Times"
  }
}
```

## ⚠️ Important Notes

1. **Keep your credentials secure** - Never commit them to public repositories
2. **Test with your own YouTube channel** - You need a channel with video history
3. **API quotas** - YouTube Analytics API has daily limits
4. **Data delays** - YouTube Analytics data can be 24-48 hours behind

---

Once you complete these steps, the authentication should work properly! 🎉
