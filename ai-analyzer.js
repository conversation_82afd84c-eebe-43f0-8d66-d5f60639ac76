/**
 * AI-powered analytics analyzer using DeepSeek V3
 * Analyzes YouTube channel data to find optimal posting times
 */

class AIAnalyzer {
    constructor() {
        this.apiKey = 'sk-4afdf7dbe6454862a44d09c4dc2d7901';
        this.baseUrl = 'https://api.deepseek.com/v1';
        this.model = 'deepseek-chat';
    }

    /**
     * Analyze channel data and generate optimal posting schedule
     */
    async analyzeOptimalPostingTimes(analyticsData) {
        try {
            console.log('🤖 Starting AI analysis of posting times...');
            
            // Prepare data for AI analysis
            const analysisData = this.prepareDataForAnalysis(analyticsData);
            
            // Create AI prompt
            const prompt = this.createAnalysisPrompt(analysisData);
            
            // Call DeepSeek API
            const aiResponse = await this.callDeepSeekAPI(prompt);
            
            // Parse AI response
            const schedule = this.parseAIResponse(aiResponse);
            
            console.log('🎯 AI Analysis Complete:', schedule);
            return schedule;
            
        } catch (error) {
            console.error('❌ AI Analysis failed:', error);
            return this.generateFallbackSchedule(analyticsData);
        }
    }

    /**
     * Prepare analytics data for AI analysis
     */
    prepareDataForAnalysis(data) {
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        
        return {
            dailyEngagement: data.dailyEngagement.map((engagement, index) => ({
                day: dayNames[index],
                dayIndex: index,
                totalEngagement: Math.round(engagement),
                relativeStrength: Math.round((engagement / Math.max(...data.dailyEngagement)) * 100)
            })),
            hourlyEngagement: data.hourlyEngagement.map((engagement, hour) => ({
                hour: hour,
                timeFormatted: this.formatHour(hour),
                engagement: Math.round(engagement),
                relativeStrength: Math.round((engagement / Math.max(...data.hourlyEngagement)) * 100)
            })),
            totalViews: data.totalViews,
            peakDay: dayNames[data.peakDay],
            peakHour: data.peakHour,
            dataSource: data.dataSource || 'youtube_analytics'
        };
    }

    /**
     * Create AI analysis prompt
     */
    createAnalysisPrompt(data) {
        return `You are a YouTube analytics expert. Analyze this channel's engagement data and recommend the BEST 2-hour posting window for EACH day of the week.

CHANNEL DATA:
Daily Engagement: ${JSON.stringify(data.dailyEngagement, null, 2)}
Hourly Engagement: ${JSON.stringify(data.hourlyEngagement, null, 2)}
Peak Day: ${data.peakDay}
Peak Hour: ${data.peakHour}:00
Total Views: ${data.totalViews}

REQUIREMENTS:
1. For EACH day (Monday-Sunday), recommend the best 2-hour posting window
2. Consider both daily engagement strength and hourly patterns
3. Provide practical posting times (avoid 2AM-6AM unless data strongly supports it)
4. Give engagement confidence score (0-100%) for each recommendation
5. Include brief reasoning for each day's recommendation

RESPONSE FORMAT (JSON only):
{
  "schedule": [
    {
      "day": "Monday",
      "dayIndex": 1,
      "startHour": 14,
      "endHour": 16,
      "timeRange": "2:00 PM - 4:00 PM",
      "confidence": 85,
      "reasoning": "Peak engagement during afternoon hours"
    },
    // ... for all 7 days
  ],
  "summary": {
    "bestOverallDay": "Thursday",
    "bestOverallTime": "6:00 PM - 8:00 PM",
    "averageConfidence": 82
  }
}

Analyze the data and respond with ONLY the JSON object.`;
    }

    /**
     * Call DeepSeek API
     */
    async callDeepSeekAPI(prompt) {
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are a YouTube analytics expert specializing in optimal posting time analysis. Always respond with valid JSON only.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        return result.choices[0].message.content;
    }

    /**
     * Parse AI response
     */
    parseAIResponse(aiResponse) {
        try {
            // Clean the response (remove any markdown formatting)
            const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
            const parsed = JSON.parse(cleanResponse);
            
            // Validate the response structure
            if (!parsed.schedule || !Array.isArray(parsed.schedule) || parsed.schedule.length !== 7) {
                throw new Error('Invalid AI response structure');
            }
            
            return parsed;
            
        } catch (error) {
            console.error('Failed to parse AI response:', error);
            console.log('Raw AI response:', aiResponse);
            throw error;
        }
    }

    /**
     * Generate fallback schedule if AI fails
     */
    generateFallbackSchedule(data) {
        console.log('🔄 Generating fallback schedule...');
        
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const schedule = [];
        
        // Find best 2-hour window for each day based on data
        dayNames.forEach((dayName, dayIndex) => {
            const dayEngagement = data.dailyEngagement[dayIndex] || 0;
            const confidence = Math.min(100, Math.round((dayEngagement / Math.max(...data.dailyEngagement)) * 100));
            
            // Default optimal times based on general YouTube patterns
            const optimalTimes = {
                0: { start: 12, end: 14 }, // Sunday: 12PM-2PM
                1: { start: 15, end: 17 }, // Monday: 3PM-5PM
                2: { start: 14, end: 16 }, // Tuesday: 2PM-4PM
                3: { start: 15, end: 17 }, // Wednesday: 3PM-5PM
                4: { start: 18, end: 20 }, // Thursday: 6PM-8PM
                5: { start: 17, end: 19 }, // Friday: 5PM-7PM
                6: { start: 13, end: 15 }  // Saturday: 1PM-3PM
            };
            
            const timeSlot = optimalTimes[dayIndex];
            
            schedule.push({
                day: dayName,
                dayIndex: dayIndex,
                startHour: timeSlot.start,
                endHour: timeSlot.end,
                timeRange: `${this.formatHour(timeSlot.start)} - ${this.formatHour(timeSlot.end)}`,
                confidence: Math.max(60, confidence),
                reasoning: `Based on ${data.dataSource === 'youtube_analytics' ? 'your channel data' : 'general YouTube patterns'}`
            });
        });
        
        return {
            schedule: schedule,
            summary: {
                bestOverallDay: dayNames[data.peakDay] || 'Thursday',
                bestOverallTime: `${this.formatHour(data.peakHour || 18)} - ${this.formatHour((data.peakHour || 18) + 2)}`,
                averageConfidence: Math.round(schedule.reduce((sum, day) => sum + day.confidence, 0) / 7)
            }
        };
    }

    /**
     * Format hour to readable time
     */
    formatHour(hour) {
        const period = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        return `${displayHour}:00 ${period}`;
    }
}

// Create global instance
const aiAnalyzer = new AIAnalyzer();
