/**
 * Configuration template for YouTube Studio Analytics Extension
 * 
 * INSTRUCTIONS:
 * 1. Copy this file to 'config.js'
 * 2. Replace the placeholder values with your actual Google Cloud credentials
 * 3. Never commit config.js to version control
 */

const CONFIG = {
    // Google Cloud OAuth 2.0 Credentials
    GOOGLE_CLIENT_ID: '260287338440-afsr4h904rvr2u960dk6nr41k0m3m308.apps.googleusercontent.com',
    GOOGLE_CLIENT_SECRET: '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',
    
    // API Configuration
    YOUTUBE_API_BASE_URL: 'https://www.googleapis.com/youtube/v3',
    YOUTUBE_ANALYTICS_API_BASE_URL: 'https://youtubeanalytics.googleapis.com/v2',
    
    // OAuth Scopes
    OAUTH_SCOPES: [
        'https://www.googleapis.com/auth/youtube.readonly',
        'https://www.googleapis.com/auth/yt-analytics.readonly'
    ],
    
    // Extension Settings
    DEFAULT_SETTINGS: {
        notifications: true,
        timezone: 'auto',
        refreshInterval: 30, // minutes
        dataRetention: 30,   // days
        cacheExpiry: 30      // minutes
    },
    
    // API Rate Limiting
    RATE_LIMITS: {
        requests_per_day: 10000,
        requests_per_100_seconds: 100,
        requests_per_100_seconds_per_user: 100
    },
    
    // Development Settings
    DEBUG_MODE: false,
    LOG_LEVEL: 'info' // 'debug', 'info', 'warn', 'error'
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
}

/**
 * SETUP INSTRUCTIONS:
 * 
 * 1. Google Cloud Console Setup:
 *    - Go to https://console.cloud.google.com/
 *    - Create a new project or select existing
 *    - Enable YouTube Analytics API and YouTube Data API v3
 *    - Create OAuth 2.0 credentials (Application type: Chrome Extension)
 *    - Configure OAuth consent screen with required scopes
 * 
 * 2. Extension Configuration:
 *    - Copy this file to 'config.js'
 *    - Replace YOUR_GOOGLE_CLIENT_ID with actual Client ID
 *    - Replace YOUR_GOOGLE_CLIENT_SECRET with actual Client Secret
 *    - Update manifest.json with the same Client ID
 * 
 * 3. Security Notes:
 *    - Never commit config.js to version control
 *    - Keep your Client Secret secure
 *    - Use environment variables in production
 *    - Regularly rotate your credentials
 * 
 * 4. Testing:
 *    - Load extension in Chrome developer mode
 *    - Test authentication flow
 *    - Verify API calls are working
 *    - Check browser console for any errors
 */
