/**
 * Content script for YouTube Studio integration
 * Enhances YouTube Studio with optimal posting time insights
 */

class YouTubeStudioEnhancer {
    constructor() {
        this.isInjected = false;
        this.observer = null;
        this.analyticsData = null;
        this.init();
    }

    /**
     * Initialize the enhancer
     */
    async init() {
        console.log('YouTube Studio Enhancer initialized');
        
        // Wait for page to load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.start());
        } else {
            this.start();
        }
    }

    /**
     * Start enhancing YouTube Studio
     */
    async start() {
        try {
            // Check if we're on YouTube Studio
            if (!this.isYouTubeStudio()) {
                return;
            }

            // Get analytics data from background
            await this.loadAnalyticsData();
            
            // Set up page observer for dynamic content
            this.setupPageObserver();
            
            // Inject enhancements
            this.injectEnhancements();
            
        } catch (error) {
            console.error('Error starting YouTube Studio enhancer:', error);
        }
    }

    /**
     * Check if current page is YouTube Studio
     */
    isYouTubeStudio() {
        return window.location.hostname === 'studio.youtube.com';
    }

    /**
     * Load analytics data from background script
     */
    async loadAnalyticsData() {
        try {
            const response = await this.sendMessage({ action: 'getAnalyticsData' });
            if (response.success && response.data) {
                this.analyticsData = response.data;
            }
        } catch (error) {
            console.error('Error loading analytics data:', error);
        }
    }

    /**
     * Set up observer for dynamic content changes
     */
    setupPageObserver() {
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    this.handlePageChange();
                }
            });
        });

        this.observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Handle page changes in YouTube Studio
     */
    handlePageChange() {
        // Debounce to avoid excessive calls
        clearTimeout(this.pageChangeTimeout);
        this.pageChangeTimeout = setTimeout(() => {
            this.injectEnhancements();
        }, 1000);
    }

    /**
     * Inject enhancements into YouTube Studio
     */
    injectEnhancements() {
        try {
            // Check current page and inject appropriate enhancements
            const currentPath = window.location.pathname;
            
            if (currentPath.includes('/content')) {
                this.enhanceContentPage();
            } else if (currentPath.includes('/analytics')) {
                this.enhanceAnalyticsPage();
            } else if (currentPath.includes('/video/')) {
                this.enhanceVideoEditPage();
            }
            
        } catch (error) {
            console.error('Error injecting enhancements:', error);
        }
    }

    /**
     * Enhance the content/videos page
     */
    enhanceContentPage() {
        if (!this.analyticsData || this.isInjected) return;
        
        // Find the main content area
        const contentArea = document.querySelector('[data-pageid="content"]') || 
                           document.querySelector('#content-area') ||
                           document.querySelector('main');
        
        if (!contentArea) return;
        
        // Create and inject optimal times widget
        const widget = this.createOptimalTimesWidget();
        if (widget) {
            this.injectWidget(contentArea, widget);
            this.isInjected = true;
        }
    }

    /**
     * Enhance the analytics page
     */
    enhanceAnalyticsPage() {
        if (!this.analyticsData) return;
        
        // Find analytics dashboard
        const analyticsArea = document.querySelector('[data-pageid="analytics"]') ||
                             document.querySelector('#analytics-content');
        
        if (!analyticsArea) return;
        
        // Add audience insights widget
        const insightsWidget = this.createAudienceInsightsWidget();
        if (insightsWidget) {
            this.injectWidget(analyticsArea, insightsWidget);
        }
    }

    /**
     * Enhance video edit page
     */
    enhanceVideoEditPage() {
        if (!this.analyticsData) return;
        
        // Find video details section
        const detailsSection = document.querySelector('#details') ||
                              document.querySelector('[data-pageid="video-details"]');
        
        if (!detailsSection) return;
        
        // Add posting time recommendation
        const recommendationWidget = this.createPostingRecommendationWidget();
        if (recommendationWidget) {
            this.injectWidget(detailsSection, recommendationWidget);
        }
    }

    /**
     * Create optimal times widget
     */
    createOptimalTimesWidget() {
        if (!this.analyticsData.bestTimes || this.analyticsData.bestTimes.length === 0) {
            return null;
        }

        const widget = document.createElement('div');
        widget.className = 'yt-analytics-widget';
        widget.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'YouTube Sans', 'Roboto', sans-serif;
        `;

        const bestTime = this.analyticsData.bestTimes[0];
        
        widget.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <div style="width: 24px; height: 24px; background: #ff0000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center;">
                    <span style="color: white; font-size: 12px; font-weight: bold;">📊</span>
                </div>
                <h3 style="margin: 0; color: #030303; font-size: 16px; font-weight: 500;">
                    🎯 Optimal Posting Times
                </h3>
            </div>
            <div style="color: #606060; font-size: 14px; line-height: 1.4;">
                <p style="margin: 0 0 8px 0;">
                    <strong>Best time to post:</strong> ${bestTime.dayName} at ${bestTime.timeFormatted}
                </p>
                <p style="margin: 0; font-size: 12px;">
                    Engagement score: ${bestTime.engagementScore}% • Based on your audience activity
                </p>
            </div>
            <div style="margin-top: 12px;">
                <button id="viewAllTimes" style="
                    background: #065fd4;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                    font-weight: 500;
                ">View All Optimal Times</button>
            </div>
        `;

        // Add click handler for view all button
        widget.querySelector('#viewAllTimes').addEventListener('click', () => {
            this.showAllOptimalTimes();
        });

        return widget;
    }

    /**
     * Create audience insights widget
     */
    createAudienceInsightsWidget() {
        const widget = document.createElement('div');
        widget.className = 'yt-analytics-insights-widget';
        widget.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 16px 0;
            font-family: 'YouTube Sans', 'Roboto', sans-serif;
        `;

        const peakDay = this.analyticsData.peakDay || 0;
        const peakHour = this.analyticsData.peakHour || 0;

        widget.innerHTML = `
            <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">
                🚀 Audience Activity Insights
            </h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div>
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 4px;">
                        ${this.getDayName(peakDay)}
                    </div>
                    <div style="font-size: 12px; opacity: 0.9;">Peak Day</div>
                </div>
                <div>
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 4px;">
                        ${this.formatTime(peakHour)}
                    </div>
                    <div style="font-size: 12px; opacity: 0.9;">Peak Hour</div>
                </div>
            </div>
        `;

        return widget;
    }

    /**
     * Create posting recommendation widget
     */
    createPostingRecommendationWidget() {
        if (!this.analyticsData.bestTimes || this.analyticsData.bestTimes.length === 0) {
            return null;
        }

        const widget = document.createElement('div');
        widget.className = 'yt-analytics-recommendation-widget';
        widget.style.cssText = `
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'YouTube Sans', 'Roboto', sans-serif;
        `;

        const nextBestTime = this.getNextBestPostingTime();
        
        widget.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">💡</span>
                <h3 style="margin: 0; color: #856404; font-size: 16px; font-weight: 500;">
                    Posting Time Recommendation
                </h3>
            </div>
            <div style="color: #856404; font-size: 14px; line-height: 1.4;">
                ${nextBestTime ? `
                    <p style="margin: 0 0 8px 0;">
                        <strong>Next optimal time:</strong> ${nextBestTime.dayName} at ${nextBestTime.timeFormatted}
                    </p>
                    <p style="margin: 0; font-size: 12px;">
                        Expected engagement: ${nextBestTime.engagementScore}%
                    </p>
                ` : `
                    <p style="margin: 0;">
                        Schedule this video for your peak audience times to maximize engagement.
                    </p>
                `}
            </div>
        `;

        return widget;
    }

    /**
     * Get next best posting time
     */
    getNextBestPostingTime() {
        if (!this.analyticsData.bestTimes) return null;
        
        const now = new Date();
        const currentDay = now.getDay();
        const currentHour = now.getHours();
        
        // Find next best time after current time
        for (const time of this.analyticsData.bestTimes) {
            if (time.day > currentDay || (time.day === currentDay && time.hour > currentHour)) {
                return time;
            }
        }
        
        // If no time found this week, return first time of next week
        return this.analyticsData.bestTimes[0];
    }

    /**
     * Inject widget into page
     */
    injectWidget(container, widget) {
        // Find a good insertion point
        const insertionPoint = container.querySelector('h1') || 
                              container.querySelector('.page-header') ||
                              container.firstElementChild;
        
        if (insertionPoint) {
            insertionPoint.parentNode.insertBefore(widget, insertionPoint.nextSibling);
        } else {
            container.prepend(widget);
        }
    }

    /**
     * Show all optimal times in a modal
     */
    showAllOptimalTimes() {
        if (!this.analyticsData.bestTimes) return;
        
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        // Create modal content
        const modal = document.createElement('div');
        modal.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            font-family: 'YouTube Sans', 'Roboto', sans-serif;
        `;
        
        const timesHtml = this.analyticsData.bestTimes.map((time, index) => `
            <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: ${index < this.analyticsData.bestTimes.length - 1 ? '1px solid #eee' : 'none'};
            ">
                <div>
                    <div style="font-weight: 600; color: #030303;">${time.dayName}</div>
                    <div style="font-size: 12px; color: #606060;">${time.timeFormatted}</div>
                </div>
                <div style="
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;
                ">${time.engagementScore}%</div>
            </div>
        `).join('');
        
        modal.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; color: #030303; font-size: 20px;">🎯 All Optimal Posting Times</h2>
                <button id="closeModal" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #606060;
                ">×</button>
            </div>
            <div>${timesHtml}</div>
            <div style="margin-top: 20px; text-align: center;">
                <button id="openExtension" style="
                    background: #065fd4;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-size: 14px;
                    cursor: pointer;
                    font-weight: 500;
                ">Open Full Analytics</button>
            </div>
        `;
        
        overlay.appendChild(modal);
        document.body.appendChild(overlay);
        
        // Add event listeners
        modal.querySelector('#closeModal').addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        
        modal.querySelector('#openExtension').addEventListener('click', () => {
            // This would trigger the extension popup
            chrome.runtime.sendMessage({ action: 'openPopup' });
            document.body.removeChild(overlay);
        });
        
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });
    }

    /**
     * Utility functions
     */
    getDayName(dayIndex) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return days[dayIndex] || 'Unknown';
    }

    formatTime(hour) {
        const period = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        return `${displayHour}:00 ${period}`;
    }

    /**
     * Send message to background script
     */
    async sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * Cleanup when page unloads
     */
    cleanup() {
        if (this.observer) {
            this.observer.disconnect();
        }
        clearTimeout(this.pageChangeTimeout);
    }
}

// Initialize the enhancer
const youtubeStudioEnhancer = new YouTubeStudioEnhancer();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    youtubeStudioEnhancer.cleanup();
});
