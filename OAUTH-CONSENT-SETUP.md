# OAuth Consent Screen Setup Guide

## 🚨 Current Issue
```
Error 403: access_denied
YT Master has not completed the Google verification process
```

## ✅ **Quick Fix: Add Test User**

### Step 1: Go to OAuth Consent Screen
1. **Google Cloud Console**: https://console.cloud.google.com/
2. **Navigate to**: APIs & Services → OAuth consent screen

### Step 2: Add Test User
1. **Scroll down to "Test users" section**
2. **Click "ADD USERS"**
3. **Enter your email**: `<EMAIL>`
4. **Click "Save"**

### Step 3: Verify Configuration
Make sure your OAuth consent screen has:

#### **App Information**
```
App name: YT Master (or YouTube Studio Analytics)
User support email: <EMAIL>
Developer contact information: <EMAIL>
```

#### **Scopes**
```
✅ https://www.googleapis.com/auth/youtube.readonly
✅ https://www.googleapis.com/auth/yt-analytics.readonly
```

#### **Test Users**
```
✅ <EMAIL>
```

## 🔧 **Complete OAuth Consent Screen Setup**

### 1. **App Information Tab**
```
App name: YouTube Studio Analytics
User support email: <EMAIL>
App logo: (optional)
App domain: (leave empty for testing)
Authorized domains: (leave empty for testing)
Developer contact information: <EMAIL>
```

### 2. **Scopes Tab**
Click "ADD OR REMOVE SCOPES" and add:
```
https://www.googleapis.com/auth/youtube.readonly
https://www.googleapis.com/auth/yt-analytics.readonly
```

### 3. **Test Users Tab**
Add your email:
```
<EMAIL>
```

### 4. **Summary Tab**
Review and save all settings.

## 🎯 **After Adding Test User**

1. **Wait 1-2 minutes** for changes to propagate
2. **Try authentication again** in your extension
3. **You should now see** the Google OAuth consent screen
4. **Grant permissions** and authentication should succeed

## 📋 **Verification Checklist**

- [ ] OAuth consent screen configured
- [ ] App name set to "YT Master" or similar
- [ ] Required scopes added
- [ ] Your email added as test user
- [ ] Changes saved
- [ ] Waited 1-2 minutes for propagation

## 🚀 **Expected Result**

After adding yourself as a test user:
1. ✅ No more "access_denied" error
2. ✅ Google OAuth consent screen appears
3. ✅ You can grant permissions
4. ✅ Authentication completes successfully
5. ✅ Extension loads your YouTube data

## ⚠️ **Important Notes**

1. **Testing Mode**: Your app is in testing mode, only test users can access it
2. **100 User Limit**: Testing mode allows up to 100 test users
3. **No Verification Required**: Testing mode doesn't require Google verification
4. **Production**: To make it public, you'd need to publish and verify the app

## 🔄 **Alternative: Publishing the App**

If you want to make it available to everyone:

1. **Complete OAuth consent screen** with all required information
2. **Click "PUBLISH APP"**
3. **Submit for verification** (required for sensitive scopes)
4. **Wait for Google approval** (can take days/weeks)

**For development/personal use, stick with testing mode and test users.**

---

**Quick fix: Just add your email as a test user and you're good to go! 🎉**
