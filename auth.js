/**
 * Authentication handler for YouTube Studio Analytics Extension
 */

class AuthManager {
    constructor() {
        this.accessToken = null;
        this.refreshToken = null;
        this.tokenExpiry = null;
        this.isAuthenticated = false;
    }

    /**
     * Initialize authentication state
     */
    async init() {
        try {
            // Check if we have stored tokens
            const storedAuth = await Utils.getData('authData');
            if (storedAuth) {
                this.accessToken = storedAuth.accessToken;
                this.refreshToken = storedAuth.refreshToken;
                this.tokenExpiry = storedAuth.tokenExpiry;
                
                // Check if token is still valid
                if (this.isTokenValid()) {
                    this.isAuthenticated = true;
                    return true;
                } else if (this.refreshToken) {
                    // Try to refresh the token
                    return await this.refreshAccessToken();
                }
            }
            return false;
        } catch (error) {
            console.error('Error initializing auth:', error);
            return false;
        }
    }

    /**
     * Start OAuth flow
     */
    async authenticate() {
        try {
            Utils.showLoading('loadingOverlay');
            
            const authUrl = this.buildAuthUrl();
            
            // Use Chrome Identity API for OAuth
            const redirectUrl = await chrome.identity.launchWebAuthFlow({
                url: authUrl,
                interactive: true
            });

            if (redirectUrl) {
                const authCode = this.extractAuthCode(redirectUrl);
                if (authCode) {
                    const tokens = await this.exchangeCodeForTokens(authCode);
                    if (tokens) {
                        await this.storeTokens(tokens);
                        this.isAuthenticated = true;
                        Utils.hideLoading('loadingOverlay');
                        return true;
                    }
                }
            }
            
            Utils.hideLoading('loadingOverlay');
            throw new Error('Authentication failed');
            
        } catch (error) {
            Utils.hideLoading('loadingOverlay');
            console.error('Authentication error:', error);
            Utils.showError('Authentication failed. Please try again.');
            return false;
        }
    }

    /**
     * Build OAuth authorization URL
     */
    buildAuthUrl() {
        const clientId = 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com'; // This should be replaced with actual client ID
        const redirectUri = chrome.identity.getRedirectURL();
        const scope = 'https://www.googleapis.com/auth/youtube.readonly https://www.googleapis.com/auth/yt-analytics.readonly';
        
        const params = new URLSearchParams({
            client_id: clientId,
            redirect_uri: redirectUri,
            response_type: 'code',
            scope: scope,
            access_type: 'offline',
            prompt: 'consent'
        });

        return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
    }

    /**
     * Extract authorization code from redirect URL
     */
    extractAuthCode(redirectUrl) {
        try {
            const url = new URL(redirectUrl);
            return url.searchParams.get('code');
        } catch (error) {
            console.error('Error extracting auth code:', error);
            return null;
        }
    }

    /**
     * Exchange authorization code for access tokens
     */
    async exchangeCodeForTokens(authCode) {
        try {
            const clientId = 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com';
            const clientSecret = 'YOUR_GOOGLE_CLIENT_SECRET'; // In production, this should be handled server-side
            const redirectUri = chrome.identity.getRedirectURL();

            const response = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    client_id: clientId,
                    client_secret: clientSecret,
                    code: authCode,
                    grant_type: 'authorization_code',
                    redirect_uri: redirectUri
                })
            });

            if (!response.ok) {
                throw new Error(`Token exchange failed: ${response.status}`);
            }

            const tokens = await response.json();
            
            if (tokens.error) {
                throw new Error(tokens.error_description || tokens.error);
            }

            return {
                accessToken: tokens.access_token,
                refreshToken: tokens.refresh_token,
                expiresIn: tokens.expires_in,
                tokenType: tokens.token_type
            };

        } catch (error) {
            console.error('Error exchanging code for tokens:', error);
            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    async refreshAccessToken() {
        try {
            if (!this.refreshToken) {
                return false;
            }

            const clientId = 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com';
            const clientSecret = 'YOUR_GOOGLE_CLIENT_SECRET';

            const response = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    client_id: clientId,
                    client_secret: clientSecret,
                    refresh_token: this.refreshToken,
                    grant_type: 'refresh_token'
                })
            });

            if (!response.ok) {
                throw new Error(`Token refresh failed: ${response.status}`);
            }

            const tokens = await response.json();
            
            if (tokens.error) {
                throw new Error(tokens.error_description || tokens.error);
            }

            // Update tokens
            this.accessToken = tokens.access_token;
            this.tokenExpiry = Date.now() + (tokens.expires_in * 1000);
            
            // Store updated tokens
            await this.storeTokens({
                accessToken: tokens.access_token,
                refreshToken: this.refreshToken, // Keep existing refresh token
                expiresIn: tokens.expires_in
            });

            this.isAuthenticated = true;
            return true;

        } catch (error) {
            console.error('Error refreshing token:', error);
            await this.signOut(); // Clear invalid tokens
            return false;
        }
    }

    /**
     * Store tokens securely
     */
    async storeTokens(tokens) {
        try {
            this.accessToken = tokens.accessToken;
            this.refreshToken = tokens.refreshToken || this.refreshToken;
            this.tokenExpiry = Date.now() + (tokens.expiresIn * 1000);

            const authData = {
                accessToken: this.accessToken,
                refreshToken: this.refreshToken,
                tokenExpiry: this.tokenExpiry
            };

            await Utils.storeData('authData', authData);
            Utils.logEvent('tokens_stored');
            
        } catch (error) {
            console.error('Error storing tokens:', error);
            throw error;
        }
    }

    /**
     * Check if current token is valid
     */
    isTokenValid() {
        return this.accessToken && 
               this.tokenExpiry && 
               Date.now() < (this.tokenExpiry - 60000); // 1 minute buffer
    }

    /**
     * Get valid access token
     */
    async getValidAccessToken() {
        if (this.isTokenValid()) {
            return this.accessToken;
        }

        if (this.refreshToken) {
            const refreshed = await this.refreshAccessToken();
            if (refreshed) {
                return this.accessToken;
            }
        }

        return null;
    }

    /**
     * Make authenticated API request
     */
    async makeAuthenticatedRequest(url, options = {}) {
        try {
            const token = await this.getValidAccessToken();
            if (!token) {
                throw new Error('No valid access token available');
            }

            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            };

            const response = await fetch(url, {
                ...options,
                headers
            });

            if (response.status === 401) {
                // Token might be invalid, try to refresh
                const refreshed = await this.refreshAccessToken();
                if (refreshed) {
                    // Retry the request with new token
                    headers['Authorization'] = `Bearer ${this.accessToken}`;
                    return await fetch(url, { ...options, headers });
                } else {
                    throw new Error('Authentication failed');
                }
            }

            return response;

        } catch (error) {
            console.error('Error making authenticated request:', error);
            throw error;
        }
    }

    /**
     * Sign out user
     */
    async signOut() {
        try {
            // Clear stored tokens
            await Utils.clearData('authData');
            
            // Reset instance variables
            this.accessToken = null;
            this.refreshToken = null;
            this.tokenExpiry = null;
            this.isAuthenticated = false;

            Utils.logEvent('user_signed_out');
            
        } catch (error) {
            console.error('Error signing out:', error);
        }
    }

    /**
     * Get authentication status
     */
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            hasValidToken: this.isTokenValid(),
            tokenExpiry: this.tokenExpiry
        };
    }
}

// Create global instance
const authManager = new AuthManager();
