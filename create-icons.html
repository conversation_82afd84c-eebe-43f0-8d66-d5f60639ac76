<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h1>Extension Icon Generator</h1>
    <p>This will create basic placeholder icons for the extension.</p>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="generateIcons()">Generate Icons</button>
    <button onclick="downloadIcons()">Download Icons</button>
    
    <script>
        function generateIcons() {
            // Generate 16x16 icon
            const canvas16 = document.getElementById('canvas16');
            const ctx16 = canvas16.getContext('2d');
            drawIcon(ctx16, 16);
            
            // Generate 48x48 icon
            const canvas48 = document.getElementById('canvas48');
            const ctx48 = canvas48.getContext('2d');
            drawIcon(ctx48, 48);
            
            // Generate 128x128 icon
            const canvas128 = document.getElementById('canvas128');
            const ctx128 = canvas128.getContext('2d');
            drawIcon(ctx128, 128);
        }
        
        function drawIcon(ctx, size) {
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#ff4757');
            gradient.addColorStop(1, '#ff3838');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // YouTube play button style
            ctx.fillStyle = 'white';
            ctx.beginPath();
            const centerX = size / 2;
            const centerY = size / 2;
            const triangleSize = size * 0.3;
            
            ctx.moveTo(centerX - triangleSize/2, centerY - triangleSize/2);
            ctx.lineTo(centerX + triangleSize/2, centerY);
            ctx.lineTo(centerX - triangleSize/2, centerY + triangleSize/2);
            ctx.closePath();
            ctx.fill();
            
            // Add analytics chart symbol
            if (size >= 48) {
                ctx.strokeStyle = 'white';
                ctx.lineWidth = size >= 128 ? 3 : 2;
                ctx.beginPath();
                const chartY = centerY + triangleSize;
                const chartHeight = size * 0.15;
                
                // Simple bar chart
                for (let i = 0; i < 3; i++) {
                    const barX = centerX - triangleSize/2 + (i * triangleSize/3);
                    const barHeight = chartHeight * (0.5 + i * 0.25);
                    ctx.moveTo(barX, chartY);
                    ctx.lineTo(barX, chartY - barHeight);
                }
                ctx.stroke();
            }
        }
        
        function downloadIcons() {
            downloadCanvas('canvas16', 'icon16.png');
            downloadCanvas('canvas48', 'icon48.png');
            downloadCanvas('canvas128', 'icon128.png');
        }
        
        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate icons on page load
        window.onload = generateIcons;
    </script>
</body>
</html>
