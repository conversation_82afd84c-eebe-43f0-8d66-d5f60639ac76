# Troubleshooting Guide - YouTube Studio Analytics Extension

## 🚨 Common Issues and Solutions

### 1. **Chrome API Errors**

#### Error: `Cannot read properties of undefined (reading 'onAlarm')`
**Solution**: ✅ **FIXED** - Added `alarms` permission to manifest.json and error handling

#### Error: `chrome.storage is undefined`
**Cause**: Missing storage permission or running outside Chrome extension context
**Solution**: 
- Ensure `storage` permission is in manifest.json
- Reload the extension in Chrome
- Check that you're running the code in the extension context

#### Error: `chrome.notifications is undefined`
**Cause**: Missing notifications permission
**Solution**: 
- Ensure `notifications` permission is in manifest.json
- Reload the extension

### 2. **Authentication Issues**

#### Error: `Authentication failed`
**Causes & Solutions**:
1. **Missing OAuth credentials**:
   - Copy `config.template.js` to `config.js`
   - Add your Google Client ID and Client Secret
   - Update manifest.json with the same Client ID

2. **Invalid OAuth setup**:
   - Go to Google Cloud Console
   - Enable YouTube Analytics API and YouTube Data API v3
   - Create OAuth 2.0 credentials for Chrome Extension
   - Add extension ID to OAuth client

3. **Incorrect scopes**:
   - Ensure these scopes are configured:
     - `https://www.googleapis.com/auth/youtube.readonly`
     - `https://www.googleapis.com/auth/yt-analytics.readonly`

#### Error: `redirect_uri_mismatch`
**Solution**:
- Get your extension ID from `chrome://extensions/`
- Add `chrome-extension://YOUR_EXTENSION_ID/` to OAuth redirect URIs

### 3. **Extension Loading Issues**

#### Error: `Manifest file is missing or unreadable`
**Solution**:
- Check that manifest.json is valid JSON
- Ensure all required fields are present
- Use a JSON validator to check syntax

#### Error: `Could not load icon`
**Solution**:
- Create icon files: `icon16.png`, `icon48.png`, `icon128.png`
- Place them in the `icons/` directory
- Use the provided `create-icons.html` to generate basic icons

#### Error: `Service worker registration failed`
**Solution**:
- Check background.js for syntax errors
- Ensure importScripts paths are correct
- Check Chrome DevTools console for specific errors

### 4. **API and Data Issues**

#### Error: `No channel found`
**Causes & Solutions**:
1. **Not signed in to YouTube**: Sign in to YouTube with the authenticated account
2. **No YouTube channel**: Create a YouTube channel for the authenticated account
3. **Insufficient permissions**: Re-authenticate with proper scopes

#### Error: `API request failed: 403`
**Causes & Solutions**:
1. **API not enabled**: Enable YouTube Analytics API in Google Cloud Console
2. **Quota exceeded**: Check API quotas in Google Cloud Console
3. **Invalid credentials**: Verify Client ID and Secret are correct

#### Error: `No data available`
**Causes & Solutions**:
1. **New channel**: Channel needs video history for analytics
2. **Insufficient data**: Upload more videos and wait for analytics data
3. **API delays**: YouTube Analytics can have 24-48 hour delays

### 5. **UI and Display Issues**

#### Error: `Popup not loading`
**Solution**:
- Check popup.html, popup.css, and popup.js for errors
- Verify all script files are loading correctly
- Check browser console for JavaScript errors

#### Error: `Heatmap not displaying`
**Solution**:
- Ensure analytics data is loading
- Check that heatmap data is being processed correctly
- Verify CSS grid styles are applied

#### Error: `Settings not saving`
**Solution**:
- Check Chrome storage permissions
- Verify storage API calls are working
- Check for JavaScript errors in settings handling

## 🔧 Debugging Steps

### 1. **Check Extension Console**
```
1. Go to chrome://extensions/
2. Find your extension
3. Click "Inspect views: background page"
4. Check Console tab for errors
```

### 2. **Check Popup Console**
```
1. Right-click extension icon
2. Select "Inspect popup"
3. Check Console tab for errors
```

### 3. **Check Content Script Console**
```
1. Go to studio.youtube.com
2. Press F12 to open DevTools
3. Check Console tab for content script errors
```

### 4. **Verify API Calls**
```
1. Open DevTools Network tab
2. Trigger API calls (refresh data)
3. Check for failed requests
4. Verify response data
```

## 🛠️ Development Mode Debugging

### Enable Debug Logging
Add this to your config.js:
```javascript
const CONFIG = {
    DEBUG_MODE: true,
    LOG_LEVEL: 'debug'
    // ... other config
};
```

### Test API Endpoints
Use this in browser console:
```javascript
// Test YouTube API
fetch('https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true', {
    headers: { 'Authorization': 'Bearer YOUR_ACCESS_TOKEN' }
})
.then(r => r.json())
.then(console.log);
```

### Check Storage Data
```javascript
// Check stored data
chrome.storage.local.get(null, console.log);

// Clear all data
chrome.storage.local.clear();
```

## 📋 Pre-Launch Checklist

Before using the extension, verify:

- [ ] ✅ `alarms` permission added to manifest.json
- [ ] ✅ Error handling added to all Chrome API calls
- [ ] Google Cloud project created
- [ ] YouTube Analytics API enabled
- [ ] YouTube Data API v3 enabled
- [ ] OAuth 2.0 credentials created
- [ ] Extension ID added to OAuth client
- [ ] config.js created with real credentials
- [ ] Icon files created in icons/ directory
- [ ] Extension loaded in Chrome developer mode
- [ ] No console errors in background script
- [ ] Authentication flow works
- [ ] API calls return data
- [ ] Popup displays correctly
- [ ] YouTube Studio integration works

## 🆘 Getting Help

### Check These Resources:
1. **Chrome Extension Documentation**: https://developer.chrome.com/docs/extensions/
2. **YouTube Analytics API**: https://developers.google.com/youtube/analytics
3. **Google OAuth 2.0**: https://developers.google.com/identity/protocols/oauth2

### Common Error Codes:
- **400**: Bad Request - Check API parameters
- **401**: Unauthorized - Re-authenticate
- **403**: Forbidden - Check API permissions/quotas
- **404**: Not Found - Check API endpoints
- **429**: Rate Limited - Wait and retry
- **500**: Server Error - Try again later

### Still Having Issues?
1. Check all console logs for specific error messages
2. Verify all setup steps were completed correctly
3. Test with a fresh Chrome profile
4. Ensure you have a YouTube channel with video history
5. Wait 24-48 hours for YouTube Analytics data to populate

---

**Note**: Most issues are related to incomplete Google Cloud setup or missing permissions. Double-check your OAuth configuration and API enablement.
