# YouTube Studio Analytics - Optimal Posting Times Chrome Extension

A powerful Chrome extension that analyzes your YouTube audience data to recommend the best times to post videos for maximum engagement. Similar to VidIQ and TubeBuddy, but focused specifically on optimal posting time analysis.

## 🌟 Features

### 🚀 Core Features
- **YouTube Channel Integration**: Direct integration with YouTube Analytics API
- **Secure OAuth Authentication**: Safe connection via Google OAuth 2.0
- **Real-Time Audience Insights**: Optimal posting time recommendations
- **Viewer Activity Heatmap**: Visual representation of audience engagement by hour
- **Smart Notifications**: Automated alerts for high-engagement posting times
- **Content Scheduling Assistance**: Export recommendations for external tools

### 📊 Analytics Dashboard
- **Best Posting Times**: Top 5 optimal times ranked by engagement score
- **Audience Activity Heatmap**: 24-hour engagement visualization
- **Quick Insights**: Peak day, peak hour, and average views
- **Historical Data Analysis**: Analyze trends over customizable time periods
- **Time Zone Management**: Automatic and manual timezone adjustments

### 🧑‍💻 User Experience
- **Simple & Intuitive UI**: Clean popup interface within Chrome
- **Single-Click Data Retrieval**: Instant analytics refresh
- **YouTube Studio Integration**: Enhanced posting recommendations directly in YouTube Studio
- **Robust Error Handling**: Clear error messages and recovery suggestions

## 📦 Installation & Setup

### Prerequisites
1. **Google Cloud Project**: You'll need a Google Cloud project with YouTube Analytics API enabled
2. **OAuth 2.0 Credentials**: Client ID and Client Secret for authentication
3. **Chrome Browser**: Version 88 or higher (Manifest V3 support)

### Step 1: Google Cloud Setup

1. **Create a Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Note your Project ID

2. **Enable Required APIs**:
   ```
   - YouTube Analytics API
   - YouTube Data API v3
   ```

3. **Create OAuth 2.0 Credentials**:
   - Go to "Credentials" in the Google Cloud Console
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: "Chrome Extension"
   - Add your extension ID (you'll get this after loading the extension)

4. **Configure OAuth Consent Screen**:
   - Add required scopes:
     - `https://www.googleapis.com/auth/youtube.readonly`
     - `https://www.googleapis.com/auth/yt-analytics.readonly`

### Step 2: Extension Configuration

1. **Update manifest.json**:
   ```json
   {
     "oauth2": {
       "client_id": "YOUR_ACTUAL_CLIENT_ID.apps.googleusercontent.com",
       "scopes": [
         "https://www.googleapis.com/auth/youtube.readonly",
         "https://www.googleapis.com/auth/yt-analytics.readonly"
       ]
     }
   }
   ```

2. **Update auth.js**:
   - Replace `YOUR_GOOGLE_CLIENT_ID` with your actual Client ID
   - Replace `YOUR_GOOGLE_CLIENT_SECRET` with your actual Client Secret

### Step 3: Load Extension in Chrome

1. **Enable Developer Mode**:
   - Open Chrome and go to `chrome://extensions/`
   - Toggle "Developer mode" in the top right

2. **Load Extension**:
   - Click "Load unpacked"
   - Select the extension directory
   - Note the Extension ID that appears

3. **Update OAuth Settings**:
   - Go back to Google Cloud Console
   - Add the Extension ID to your OAuth 2.0 client configuration

### Step 4: Add Extension Icons

Create the following icon files in the `icons/` directory:
- `icon16.png` (16x16 pixels)
- `icon48.png` (48x48 pixels)  
- `icon128.png` (128x128 pixels)

You can use any image editing software to create these icons with a YouTube/analytics theme.

## 🚀 Usage

### First Time Setup
1. Click the extension icon in Chrome toolbar
2. Click "Sign in with Google"
3. Authorize the required permissions
4. Wait for initial data analysis (may take a few moments)

### Daily Usage
1. **View Optimal Times**: Click extension icon to see best posting times
2. **Check Heatmap**: Visual representation of audience activity
3. **Get Notifications**: Receive alerts when optimal posting times approach
4. **YouTube Studio Integration**: See recommendations directly in YouTube Studio

### Settings Configuration
- **Time Zone**: Set your target audience timezone
- **Notifications**: Enable/disable posting time alerts
- **Data Refresh**: Configure how often data updates

## 🔧 Development

### Project Structure
```
├── manifest.json          # Extension configuration
├── popup.html             # Main UI interface
├── popup.css              # Styling
├── popup.js               # Frontend logic
├── background.js          # Service worker
├── content.js             # YouTube Studio integration
├── auth.js                # OAuth authentication
├── analytics.js           # YouTube Analytics API
├── utils.js               # Utility functions
├── icons/                 # Extension icons
└── README.md              # This file
```

### Key Components

1. **Authentication (auth.js)**:
   - OAuth 2.0 flow management
   - Token storage and refresh
   - Secure API request handling

2. **Analytics (analytics.js)**:
   - YouTube Analytics API integration
   - Data processing and analysis
   - Optimal time calculations

3. **Background Service (background.js)**:
   - Periodic data refresh
   - Notification scheduling
   - Settings management

4. **Content Script (content.js)**:
   - YouTube Studio page enhancement
   - Real-time posting recommendations
   - UI injection and interaction

### API Endpoints Used
- **YouTube Analytics API v2**: Audience retention and engagement data
- **YouTube Data API v3**: Channel information and metadata

## 🔒 Privacy & Security

### Data Handling
- **No Data Storage**: Analytics data is cached temporarily only
- **Secure Authentication**: OAuth 2.0 with minimal required permissions
- **Local Processing**: All analysis happens locally in the browser
- **Transparent Permissions**: Clear explanation of required access

### Permissions Required
- `identity`: For Google OAuth authentication
- `storage`: For settings and temporary data caching
- `notifications`: For optimal posting time alerts
- `activeTab`: For YouTube Studio integration

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Failed**:
   - Verify OAuth 2.0 credentials are correct
   - Check that APIs are enabled in Google Cloud
   - Ensure extension ID is added to OAuth client

2. **No Data Available**:
   - Channel must have sufficient video history
   - YouTube Analytics API may have delays
   - Check API quotas in Google Cloud Console

3. **Extension Not Loading**:
   - Verify all files are present
   - Check Chrome developer console for errors
   - Ensure Manifest V3 compatibility

### Debug Mode
Enable debug logging by opening Chrome DevTools:
1. Right-click extension icon → "Inspect popup"
2. Check Console tab for detailed logs
3. Background script logs: `chrome://extensions/` → Extension details → "Inspect views: background page"

## 📈 Future Enhancements

### Planned Features
- **Predictive Analytics**: Machine learning for trend forecasting
- **Competitive Benchmarking**: Compare with similar channels
- **Mobile Extension**: Companion mobile app
- **Advanced Scheduling**: Direct integration with scheduling tools
- **Multi-Channel Support**: Manage multiple YouTube channels

### Contributing
This is a private project, but suggestions and feedback are welcome through GitHub issues.

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:
1. Check the troubleshooting section above
2. Review Chrome extension developer documentation
3. Verify Google Cloud API configuration
4. Check YouTube Analytics API documentation

---

**Note**: This extension requires a valid YouTube channel with sufficient video history to provide meaningful analytics. Initial setup may take some time as the system analyzes your audience data.
