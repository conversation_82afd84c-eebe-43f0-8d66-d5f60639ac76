/**
 * YouTube Analytics API integration for optimal posting times
 */

class YouTubeAnalytics {
    constructor() {
        this.baseUrl = 'https://youtubeanalytics.googleapis.com/v2';
        this.youtubeApiUrl = 'https://www.googleapis.com/youtube/v3';
        this.channelId = null;
        this.cachedData = null;
        this.cacheExpiry = null;
    }

    /**
     * Initialize analytics by getting channel information
     */
    async init() {
        try {
            const channelInfo = await this.getChannelInfo();
            if (channelInfo) {
                this.channelId = channelInfo.id;
                return channelInfo;
            }
            return null;
        } catch (error) {
            console.error('Error initializing analytics:', error);
            throw error;
        }
    }

    /**
     * Get channel information
     */
    async getChannelInfo() {
        try {
            const url = `${this.youtubeApiUrl}/channels?part=snippet,statistics&mine=true`;
            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            
            if (!Utils.validateApiResponse(data) || !data.items || data.items.length === 0) {
                throw new Error('No channel found');
            }

            const channel = data.items[0];
            return {
                id: channel.id,
                title: channel.snippet.title,
                description: channel.snippet.description,
                thumbnail: channel.snippet.thumbnails.default.url,
                subscriberCount: parseInt(channel.statistics.subscriberCount) || 0,
                videoCount: parseInt(channel.statistics.videoCount) || 0,
                viewCount: parseInt(channel.statistics.viewCount) || 0
            };

        } catch (error) {
            console.error('Error getting channel info:', error);
            throw error;
        }
    }

    /**
     * Get audience retention data for optimal posting times
     */
    async getAudienceRetentionData(days = 30) {
        try {
            if (!this.channelId) {
                await this.init();
            }

            // Check cache first
            if (this.isCacheValid()) {
                return this.cachedData;
            }

            const dateRange = Utils.getDateRange(days);
            console.log('Getting audience retention data for date range:', dateRange);

            let analyticsData = null;

            try {
                // Try to get basic analytics data (just views by day)
                analyticsData = await this.getBasicAnalytics(dateRange);
                console.log('Successfully got analytics data');
            } catch (error) {
                console.warn('Failed to get analytics data:', error.message);
                console.log('Using mock data for demonstration');
                return this.generateMockData();
            }

            // Process the analytics data
            const processedData = this.processRealAnalyticsData(analyticsData);

            // Cache the results
            this.cacheData(processedData);

            return processedData;

        } catch (error) {
            console.error('Error getting audience retention data:', error);
            // Return mock data as fallback
            return this.generateMockData();
        }
    }

    /**
     * Get basic analytics data (views by day)
     */
    async getBasicAnalytics(dateRange) {
        try {
            // Use the most basic metrics available in YouTube Analytics API
            const metrics = 'views';
            const dimensions = 'day';

            console.log('Requesting basic analytics with:', {
                channelId: this.channelId,
                dateRange,
                metrics,
                dimensions
            });

            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'day'
            });

            console.log('Analytics API URL:', url);

            const response = await authManager.makeAuthenticatedRequest(url);

            console.log('Analytics API Response Status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Analytics API Error Response:', errorText);

                // Check if it's a permissions or data availability issue
                if (response.status === 403) {
                    console.warn('YouTube Analytics API access denied - may need channel verification or insufficient data');
                } else if (response.status === 400) {
                    console.warn('Bad request - channel may not have sufficient analytics data');
                }

                throw new Error(`Analytics request failed: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('Analytics API Response Data:', data);

            // Validate the response structure
            if (!data.rows || data.rows.length === 0) {
                console.warn('No analytics data available - channel may be too new or have insufficient data');
                throw new Error('No analytics data available');
            }

            return data;

        } catch (error) {
            console.error('Error getting basic analytics:', error);
            throw error;
        }
    }

    /**
     * Get daily analytics data
     */
    async getDailyAnalytics(dateRange) {
        try {
            // Use basic metrics that should be available for most channels
            const metrics = 'views,estimatedMinutesWatched';
            const dimensions = 'day';

            console.log('Requesting daily analytics with:', {
                channelId: this.channelId,
                dateRange,
                metrics,
                dimensions
            });

            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'day'
            });

            console.log('Daily Analytics API URL:', url);

            const response = await authManager.makeAuthenticatedRequest(url);

            console.log('Daily Analytics Response Status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Daily Analytics Error Response:', errorText);
                throw new Error(`Daily analytics request failed: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('Daily Analytics Response Data:', data);
            return data;

        } catch (error) {
            console.error('Error getting daily analytics:', error);
            throw error;
        }
    }

    /**
     * Process real analytics data from YouTube Analytics API
     */
    processRealAnalyticsData(analyticsData) {
        try {
            console.log('Processing real analytics data:', analyticsData);

            const processed = {
                hourlyEngagement: new Array(24).fill(0),
                dailyEngagement: new Array(7).fill(0),
                bestTimes: [],
                peakHour: 0,
                peakDay: 0,
                totalViews: 0,
                totalWatchTime: 0,
                averageViewDuration: 0,
                heatmapData: [],
                isRealData: true, // Flag to indicate this is real data
                dataSource: 'youtube_analytics'
            };

            // Process the real data from YouTube Analytics API
            if (analyticsData && analyticsData.rows && analyticsData.rows.length > 0) {
                console.log('Processing real analytics rows:', analyticsData.rows.length);

                analyticsData.rows.forEach((row, index) => {
                    const date = new Date(row[0]);
                    const dayOfWeek = date.getDay();
                    const views = parseInt(row[1]) || 0;

                    console.log(`Real data - Day ${index}:`, {
                        date: row[0],
                        dayOfWeek,
                        dayName: Utils.getDayName(dayOfWeek),
                        views
                    });

                    processed.dailyEngagement[dayOfWeek] += views;
                    processed.totalViews += views;

                    // Create realistic hourly distribution based on common patterns
                    const hourlyDistribution = this.generateHourlyDistribution(views);
                    hourlyDistribution.forEach((hourViews, hour) => {
                        processed.hourlyEngagement[hour] += hourViews;
                    });
                });

                // Find peak times
                processed.peakHour = processed.hourlyEngagement.indexOf(Math.max(...processed.hourlyEngagement));
                processed.peakDay = processed.dailyEngagement.indexOf(Math.max(...processed.dailyEngagement));

                // Generate best posting times based on real data patterns
                processed.bestTimes = this.calculateBestPostingTimes(processed);

                // Generate heatmap data
                processed.heatmapData = this.generateHeatmapData(processed);

                console.log('📊 Real Data Summary:');
                console.log('Daily Engagement:', processed.dailyEngagement);
                console.log('Peak Day:', processed.peakDay, '(' + Utils.getDayName(processed.peakDay) + ')');
                console.log('Peak Hour:', processed.peakHour);
                console.log('Best Times:', processed.bestTimes);
                console.log('Processed real data:', processed);
                return processed;

            } else {
                console.log('No real data available, falling back to mock data');
                return this.generateMockData();
            }

        } catch (error) {
            console.error('Error processing real analytics data:', error);
            return this.generateMockData();
        }
    }

    /**
     * Generate realistic hourly distribution from daily views
     */
    generateHourlyDistribution(dailyViews) {
        // Common YouTube viewing patterns (based on research)
        const hourlyPatterns = [
            0.02, 0.01, 0.01, 0.01, 0.02, 0.03, 0.04, 0.05, // 12AM-7AM
            0.06, 0.07, 0.06, 0.07, 0.08, 0.07, 0.06, 0.07, // 8AM-3PM
            0.08, 0.09, 0.10, 0.09, 0.08, 0.07, 0.05, 0.03  // 4PM-11PM
        ];

        return hourlyPatterns.map(pattern => Math.round(dailyViews * pattern));
    }

    /**
     * Process audience data to find optimal posting times (legacy method)
     */
    processAudienceData(hourlyData, dailyData) {
        try {
            const processed = {
                hourlyEngagement: new Array(24).fill(0),
                dailyEngagement: new Array(7).fill(0),
                bestTimes: [],
                peakHour: 0,
                peakDay: 0,
                totalViews: 0,
                totalWatchTime: 0,
                averageViewDuration: 0,
                heatmapData: []
            };

            console.log('Processing audience data:', { hourlyData, dailyData });

            // Process daily data (we're using daily data for both hourly and daily for now)
            if (dailyData && dailyData.rows && dailyData.rows.length > 0) {
                console.log('Processing daily data rows:', dailyData.rows.length);

                dailyData.rows.forEach((row, index) => {
                    const date = new Date(row[0]);
                    const dayOfWeek = date.getDay();
                    const views = parseInt(row[1]) || 0;
                    const watchTime = parseInt(row[2]) || 0;

                    console.log(`Day ${index}:`, { date: row[0], dayOfWeek, views, watchTime });

                    processed.dailyEngagement[dayOfWeek] += views + (watchTime / 60);
                    processed.totalViews += views;
                    processed.totalWatchTime += watchTime;

                    // Distribute daily data across hours (mock hourly distribution)
                    const peakHours = [9, 12, 15, 18, 20]; // Common peak hours
                    peakHours.forEach(hour => {
                        processed.hourlyEngagement[hour] += (views + watchTime / 60) / peakHours.length;
                    });
                });
            } else {
                console.log('No daily data available, using mock data');
                return this.generateMockData();
            }

            // Find peak times
            processed.peakHour = processed.hourlyEngagement.indexOf(Math.max(...processed.hourlyEngagement));
            processed.peakDay = processed.dailyEngagement.indexOf(Math.max(...processed.dailyEngagement));

            // Calculate average view duration
            processed.averageViewDuration = processed.totalViews > 0 ?
                Math.round(processed.totalWatchTime / processed.totalViews) : 0;

            // Generate best posting times
            processed.bestTimes = this.calculateBestPostingTimes(processed);

            // Generate heatmap data
            processed.heatmapData = this.generateHeatmapData(processed);

            console.log('Processed data:', processed);
            return processed;

        } catch (error) {
            console.error('Error processing audience data:', error);
            return this.generateMockData();
        }
    }

    /**
     * Calculate best posting times based on engagement data
     */
    calculateBestPostingTimes(data) {
        const bestTimes = [];
        
        // Find top 5 hour-day combinations
        for (let day = 0; day < 7; day++) {
            for (let hour = 0; hour < 24; hour++) {
                const dayEngagement = data.dailyEngagement[day];
                const hourEngagement = data.hourlyEngagement[hour];
                const combinedScore = (dayEngagement * 0.6) + (hourEngagement * 0.4);
                
                bestTimes.push({
                    day: day,
                    hour: hour,
                    score: combinedScore,
                    dayName: Utils.getDayName(day),
                    timeFormatted: Utils.formatTime(hour)
                });
            }
        }

        // Sort by score and return top 5
        return bestTimes
            .sort((a, b) => b.score - a.score)
            .slice(0, 5)
            .map(time => ({
                ...time,
                engagementScore: Math.min(100, Math.round((time.score / Math.max(...bestTimes.map(t => t.score))) * 100))
            }));
    }

    /**
     * Generate heatmap data for visualization
     */
    generateHeatmapData(data) {
        const heatmap = [];
        const maxEngagement = Math.max(...data.hourlyEngagement);
        
        for (let hour = 0; hour < 24; hour++) {
            const engagement = data.hourlyEngagement[hour];
            const intensity = maxEngagement > 0 ? engagement / maxEngagement : 0;
            
            heatmap.push({
                hour: hour,
                intensity: intensity,
                color: Utils.getHeatmapColor(intensity),
                tooltip: `${Utils.formatTime(hour)}: ${Utils.formatNumber(engagement)} engagement`
            });
        }
        
        return heatmap;
    }

    /**
     * Get video performance data
     */
    async getVideoPerformance(videoId) {
        try {
            const dateRange = Utils.getDateRange(30);
            const metrics = 'views,likes,dislikes,comments,shares,estimatedMinutesWatched';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                filters: `video==${videoId}`
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Video performance request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting video performance:', error);
            throw error;
        }
    }

    /**
     * Cache data to avoid excessive API calls
     */
    cacheData(data) {
        this.cachedData = data;
        this.cacheExpiry = Date.now() + (30 * 60 * 1000); // Cache for 30 minutes
        
        // Also store in Chrome storage for persistence
        Utils.storeData('analyticsCache', {
            data: data,
            expiry: this.cacheExpiry
        });
    }

    /**
     * Check if cached data is still valid
     */
    isCacheValid() {
        return this.cachedData && 
               this.cacheExpiry && 
               Date.now() < this.cacheExpiry;
    }

    /**
     * Load cached data from storage
     */
    async loadCachedData() {
        try {
            const cached = await Utils.getData('analyticsCache');
            if (cached && cached.expiry && Date.now() < cached.expiry) {
                this.cachedData = cached.data;
                this.cacheExpiry = cached.expiry;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error loading cached data:', error);
            return false;
        }
    }

    /**
     * Clear cached data
     */
    async clearCache() {
        this.cachedData = null;
        this.cacheExpiry = null;
        await Utils.clearData('analyticsCache');
    }

    /**
     * Get subscriber growth data
     */
    async getSubscriberGrowth(days = 30) {
        try {
            const dateRange = Utils.getDateRange(days);
            const metrics = 'subscribersGained,subscribersLost';
            const dimensions = 'day';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'day'
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Subscriber growth request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting subscriber growth:', error);
            throw error;
        }
    }

    /**
     * Generate mock data for demonstration purposes
     */
    generateMockData() {
        console.log('🎭 Generating mock analytics data for demonstration');
        console.log('📝 Note: This is sample data to show how the extension works');
        console.log('📊 Real data will appear once your channel has sufficient analytics history');

        // Generate realistic mock data based on common YouTube patterns
        const hourlyEngagement = new Array(24).fill(0);
        const dailyEngagement = new Array(7).fill(0);

        // Mock hourly pattern (higher engagement in evening)
        hourlyEngagement[9] = 1200;   // 9 AM
        hourlyEngagement[12] = 1800;  // 12 PM
        hourlyEngagement[15] = 1500;  // 3 PM
        hourlyEngagement[18] = 2200;  // 6 PM (peak)
        hourlyEngagement[20] = 2000;  // 8 PM
        hourlyEngagement[21] = 1600;  // 9 PM

        // Fill other hours with lower values
        for (let i = 0; i < 24; i++) {
            if (hourlyEngagement[i] === 0) {
                hourlyEngagement[i] = Math.random() * 500 + 200;
            }
        }

        // Mock daily pattern (higher on weekends)
        dailyEngagement[0] = 8000;  // Sunday
        dailyEngagement[1] = 6000;  // Monday
        dailyEngagement[2] = 6500;  // Tuesday
        dailyEngagement[3] = 7000;  // Wednesday
        dailyEngagement[4] = 7200;  // Thursday
        dailyEngagement[5] = 7800;  // Friday
        dailyEngagement[6] = 8500;  // Saturday (peak)

        const processed = {
            hourlyEngagement,
            dailyEngagement,
            peakHour: 18, // 6 PM
            peakDay: 6,   // Saturday
            totalViews: 45000,
            totalWatchTime: 180000, // minutes
            averageViewDuration: 4, // minutes
            bestTimes: [],
            heatmapData: [],
            isRealData: false, // Flag to indicate this is mock data
            dataSource: 'mock'
        };

        // Generate best posting times
        processed.bestTimes = this.calculateBestPostingTimes(processed);

        // Generate heatmap data
        processed.heatmapData = this.generateHeatmapData(processed);

        return processed;
    }
}

// Create global instance
const youtubeAnalytics = new YouTubeAnalytics();
