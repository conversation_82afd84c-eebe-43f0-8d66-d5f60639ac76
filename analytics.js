/**
 * YouTube Analytics API integration for optimal posting times
 */

class YouTubeAnalytics {
    constructor() {
        this.baseUrl = 'https://youtubeanalytics.googleapis.com/v2';
        this.youtubeApiUrl = 'https://www.googleapis.com/youtube/v3';
        this.channelId = null;
        this.cachedData = null;
        this.cacheExpiry = null;
    }

    /**
     * Initialize analytics by getting channel information
     */
    async init() {
        try {
            const channelInfo = await this.getChannelInfo();
            if (channelInfo) {
                this.channelId = channelInfo.id;
                return channelInfo;
            }
            return null;
        } catch (error) {
            console.error('Error initializing analytics:', error);
            throw error;
        }
    }

    /**
     * Get channel information
     */
    async getChannelInfo() {
        try {
            const url = `${this.youtubeApiUrl}/channels?part=snippet,statistics&mine=true`;
            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            
            if (!Utils.validateApiResponse(data) || !data.items || data.items.length === 0) {
                throw new Error('No channel found');
            }

            const channel = data.items[0];
            return {
                id: channel.id,
                title: channel.snippet.title,
                description: channel.snippet.description,
                thumbnail: channel.snippet.thumbnails.default.url,
                subscriberCount: parseInt(channel.statistics.subscriberCount) || 0,
                videoCount: parseInt(channel.statistics.videoCount) || 0,
                viewCount: parseInt(channel.statistics.viewCount) || 0
            };

        } catch (error) {
            console.error('Error getting channel info:', error);
            throw error;
        }
    }

    /**
     * Get audience retention data for optimal posting times
     */
    async getAudienceRetentionData(days = 30) {
        try {
            if (!this.channelId) {
                await this.init();
            }

            // Check cache first
            if (this.isCacheValid()) {
                return this.cachedData;
            }

            const dateRange = Utils.getDateRange(days);
            
            // Get hourly audience retention data
            const hourlyData = await this.getHourlyAnalytics(dateRange);
            
            // Get daily audience retention data
            const dailyData = await this.getDailyAnalytics(dateRange);
            
            // Process and combine data
            const processedData = this.processAudienceData(hourlyData, dailyData);
            
            // Cache the results
            this.cacheData(processedData);
            
            return processedData;

        } catch (error) {
            console.error('Error getting audience retention data:', error);
            throw error;
        }
    }

    /**
     * Get hourly analytics data
     */
    async getHourlyAnalytics(dateRange) {
        try {
            const metrics = 'views,estimatedMinutesWatched,averageViewDuration';
            const dimensions = 'hour';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'hour'
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Hourly analytics request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting hourly analytics:', error);
            throw error;
        }
    }

    /**
     * Get daily analytics data
     */
    async getDailyAnalytics(dateRange) {
        try {
            const metrics = 'views,estimatedMinutesWatched,averageViewDuration';
            const dimensions = 'day';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'day'
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Daily analytics request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting daily analytics:', error);
            throw error;
        }
    }

    /**
     * Process audience data to find optimal posting times
     */
    processAudienceData(hourlyData, dailyData) {
        try {
            const processed = {
                hourlyEngagement: new Array(24).fill(0),
                dailyEngagement: new Array(7).fill(0),
                bestTimes: [],
                peakHour: 0,
                peakDay: 0,
                totalViews: 0,
                totalWatchTime: 0,
                averageViewDuration: 0,
                heatmapData: []
            };

            // Process hourly data
            if (hourlyData && hourlyData.rows) {
                hourlyData.rows.forEach(row => {
                    const hour = parseInt(row[0]);
                    const views = parseInt(row[1]) || 0;
                    const watchTime = parseInt(row[2]) || 0;
                    
                    processed.hourlyEngagement[hour] = views + (watchTime / 60); // Combine views and watch time
                    processed.totalViews += views;
                    processed.totalWatchTime += watchTime;
                });
            }

            // Process daily data
            if (dailyData && dailyData.rows) {
                dailyData.rows.forEach(row => {
                    const date = new Date(row[0]);
                    const dayOfWeek = date.getDay();
                    const views = parseInt(row[1]) || 0;
                    const watchTime = parseInt(row[2]) || 0;
                    
                    processed.dailyEngagement[dayOfWeek] += views + (watchTime / 60);
                });
            }

            // Find peak times
            processed.peakHour = processed.hourlyEngagement.indexOf(Math.max(...processed.hourlyEngagement));
            processed.peakDay = processed.dailyEngagement.indexOf(Math.max(...processed.dailyEngagement));

            // Calculate average view duration
            processed.averageViewDuration = processed.totalViews > 0 ? 
                Math.round(processed.totalWatchTime / processed.totalViews) : 0;

            // Generate best posting times
            processed.bestTimes = this.calculateBestPostingTimes(processed);

            // Generate heatmap data
            processed.heatmapData = this.generateHeatmapData(processed);

            return processed;

        } catch (error) {
            console.error('Error processing audience data:', error);
            throw error;
        }
    }

    /**
     * Calculate best posting times based on engagement data
     */
    calculateBestPostingTimes(data) {
        const bestTimes = [];
        
        // Find top 5 hour-day combinations
        for (let day = 0; day < 7; day++) {
            for (let hour = 0; hour < 24; hour++) {
                const dayEngagement = data.dailyEngagement[day];
                const hourEngagement = data.hourlyEngagement[hour];
                const combinedScore = (dayEngagement * 0.6) + (hourEngagement * 0.4);
                
                bestTimes.push({
                    day: day,
                    hour: hour,
                    score: combinedScore,
                    dayName: Utils.getDayName(day),
                    timeFormatted: Utils.formatTime(hour)
                });
            }
        }

        // Sort by score and return top 5
        return bestTimes
            .sort((a, b) => b.score - a.score)
            .slice(0, 5)
            .map(time => ({
                ...time,
                engagementScore: Math.min(100, Math.round((time.score / Math.max(...bestTimes.map(t => t.score))) * 100))
            }));
    }

    /**
     * Generate heatmap data for visualization
     */
    generateHeatmapData(data) {
        const heatmap = [];
        const maxEngagement = Math.max(...data.hourlyEngagement);
        
        for (let hour = 0; hour < 24; hour++) {
            const engagement = data.hourlyEngagement[hour];
            const intensity = maxEngagement > 0 ? engagement / maxEngagement : 0;
            
            heatmap.push({
                hour: hour,
                intensity: intensity,
                color: Utils.getHeatmapColor(intensity),
                tooltip: `${Utils.formatTime(hour)}: ${Utils.formatNumber(engagement)} engagement`
            });
        }
        
        return heatmap;
    }

    /**
     * Get video performance data
     */
    async getVideoPerformance(videoId) {
        try {
            const dateRange = Utils.getDateRange(30);
            const metrics = 'views,likes,dislikes,comments,shares,estimatedMinutesWatched';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                filters: `video==${videoId}`
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Video performance request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting video performance:', error);
            throw error;
        }
    }

    /**
     * Cache data to avoid excessive API calls
     */
    cacheData(data) {
        this.cachedData = data;
        this.cacheExpiry = Date.now() + (30 * 60 * 1000); // Cache for 30 minutes
        
        // Also store in Chrome storage for persistence
        Utils.storeData('analyticsCache', {
            data: data,
            expiry: this.cacheExpiry
        });
    }

    /**
     * Check if cached data is still valid
     */
    isCacheValid() {
        return this.cachedData && 
               this.cacheExpiry && 
               Date.now() < this.cacheExpiry;
    }

    /**
     * Load cached data from storage
     */
    async loadCachedData() {
        try {
            const cached = await Utils.getData('analyticsCache');
            if (cached && cached.expiry && Date.now() < cached.expiry) {
                this.cachedData = cached.data;
                this.cacheExpiry = cached.expiry;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error loading cached data:', error);
            return false;
        }
    }

    /**
     * Clear cached data
     */
    async clearCache() {
        this.cachedData = null;
        this.cacheExpiry = null;
        await Utils.clearData('analyticsCache');
    }

    /**
     * Get subscriber growth data
     */
    async getSubscriberGrowth(days = 30) {
        try {
            const dateRange = Utils.getDateRange(days);
            const metrics = 'subscribersGained,subscribersLost';
            const dimensions = 'day';
            
            const url = `${this.baseUrl}/reports?` + new URLSearchParams({
                ids: `channel==${this.channelId}`,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                metrics: metrics,
                dimensions: dimensions,
                sort: 'day'
            });

            const response = await authManager.makeAuthenticatedRequest(url);
            
            if (!response.ok) {
                throw new Error(`Subscriber growth request failed: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Error getting subscriber growth:', error);
            throw error;
        }
    }
}

// Create global instance
const youtubeAnalytics = new YouTubeAnalytics();
