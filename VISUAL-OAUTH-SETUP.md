# Visual OAuth Setup Guide

## 🚨 Current Issue
You have a "Chrome extension" OAuth client, but Chrome extensions need "Web application" OAuth clients for redirect URIs.

## ✅ Solution Steps

### Step 1: Create New Web Application OAuth Client

1. **In Google Cloud Console** → **APIs & Services** → **Credentials**
2. **Click "Create Credentials"** → **"OAuth 2.0 Client IDs"**

3. **Select Application Type**:
   ```
   ❌ Chrome extension  (This is what you have now - doesn't support redirect URIs)
   ✅ Web application   (This is what you need - supports redirect URIs)
   ```

4. **Fill in the form**:
   ```
   Name: YouTube Studio Analytics Web Client
   
   Authorized JavaScript origins: (leave empty)
   
   Authorized redirect URIs: 
   https://oplnepppfljkeipgkdaffgcpbleffknk.chromiumapp.org/
   ```

### Step 2: What You'll See

**After creating the Web application client, you'll see:**

```
Client ID: 123456789012-abc<PERSON>fghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
Client Secret: GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

Authorized redirect URIs:
✅ https://oplnepppfljkeipgkdaffgcpbleffknk.chromiumapp.org/
```

### Step 3: Update Your Extension

1. **Copy the new Client ID** from the Web application client
2. **Replace in manifest.json**:
   ```json
   "oauth2": {
     "client_id": "YOUR_NEW_WEB_CLIENT_ID.apps.googleusercontent.com",
     "scopes": [
       "https://www.googleapis.com/auth/youtube.readonly",
       "https://www.googleapis.com/auth/yt-analytics.readonly"
     ]
   }
   ```

### Step 4: Test

1. **Reload extension** in Chrome
2. **Try authentication** - should work now!

## 🔍 Why This Happens

**Chrome Extension OAuth Client:**
- ❌ No redirect URI configuration option
- ❌ Limited functionality in Manifest V3
- ❌ Can't handle custom redirect URIs

**Web Application OAuth Client:**
- ✅ Full redirect URI configuration
- ✅ Works with Chrome extensions
- ✅ Supports all OAuth flows

## 📋 Quick Checklist

- [ ] Create new "Web application" OAuth client
- [ ] Add redirect URI: `https://oplnepppfljkeipgkdaffgcpbleffknk.chromiumapp.org/`
- [ ] Copy new Client ID
- [ ] Update manifest.json with new Client ID
- [ ] Reload extension in Chrome
- [ ] Test authentication

## 🎯 Expected Result

After following these steps:
1. ✅ No more "redirect_uri_mismatch" error
2. ✅ Google OAuth consent screen appears
3. ✅ You can grant permissions
4. ✅ Authentication completes successfully
5. ✅ Extension loads your YouTube data

## ⚠️ Important Notes

1. **Keep both clients**: You can keep the old Chrome extension client and create a new Web application client
2. **Use the Web client**: Only use the Web application client ID in your extension
3. **Exact URI match**: The redirect URI must be exactly: `https://oplnepppfljkeipgkdaffgcpbleffknk.chromiumapp.org/`
4. **Case sensitive**: Make sure the extension ID case matches exactly

---

**This will definitely fix your redirect_uri_mismatch error! 🎉**
