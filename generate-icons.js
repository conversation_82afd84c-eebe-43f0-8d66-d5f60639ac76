/**
 * Simple script to generate basic extension icons
 * Run this in a browser console or as a Node.js script
 */

function generateIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // Background gradient (YouTube red)
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#ff4757');
    gradient.addColorStop(1, '#ff3838');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);
    
    // White play button
    ctx.fillStyle = 'white';
    ctx.beginPath();
    const centerX = size / 2;
    const centerY = size / 2;
    const triangleSize = size * 0.25;
    
    ctx.moveTo(centerX - triangleSize/2, centerY - triangleSize/2);
    ctx.lineTo(centerX + triangleSize/2, centerY);
    ctx.lineTo(centerX - triangleSize/2, centerY + triangleSize/2);
    ctx.closePath();
    ctx.fill();
    
    // Analytics chart (for larger icons)
    if (size >= 48) {
        ctx.strokeStyle = 'white';
        ctx.lineWidth = size >= 128 ? 3 : 2;
        ctx.beginPath();
        
        const chartY = centerY + triangleSize * 0.8;
        const chartHeight = size * 0.12;
        const barWidth = size * 0.08;
        
        // Three bars representing analytics
        for (let i = 0; i < 3; i++) {
            const barX = centerX - barWidth + (i * barWidth * 0.8);
            const barHeight = chartHeight * (0.4 + i * 0.3);
            
            ctx.fillRect(barX, chartY - barHeight, barWidth * 0.6, barHeight);
        }
    }
    
    return canvas.toDataURL('image/png');
}

// Generate icons if running in browser
if (typeof document !== 'undefined') {
    console.log('Generating extension icons...');
    
    // Create download links
    const sizes = [16, 48, 128];
    sizes.forEach(size => {
        const dataUrl = generateIcon(size);
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `icon${size}.png`;
        link.textContent = `Download icon${size}.png`;
        link.style.display = 'block';
        link.style.margin = '10px';
        document.body.appendChild(link);
    });
    
    console.log('Icons generated! Click the links to download.');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generateIcon };
}
