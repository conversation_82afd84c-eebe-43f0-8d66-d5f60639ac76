/**
 * Main popup script for YouTube Studio Analytics Extension
 */

class PopupManager {
    constructor() {
        this.isLoading = false;
        this.currentData = null;
        this.settings = null;
        this.init();
    }

    /**
     * Initialize popup
     */
    async init() {
        console.log('Popup initialized');
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load settings
        await this.loadSettings();
        
        // Check authentication status
        await this.checkAuthStatus();
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Authentication button
        const authBtn = document.getElementById('authBtn');
        if (authBtn) {
            authBtn.addEventListener('click', () => this.handleAuthentication());
        }

        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // Retry button
        const retryBtn = document.getElementById('retryBtn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => this.retryLastAction());
        }

        // Settings
        const timezoneSelect = document.getElementById('timezone');
        if (timezoneSelect) {
            timezoneSelect.addEventListener('change', (e) => this.updateTimezone(e.target.value));
        }

        const notificationsCheckbox = document.getElementById('notifications');
        if (notificationsCheckbox) {
            notificationsCheckbox.addEventListener('change', (e) => this.updateNotifications(e.target.checked));
        }
    }

    /**
     * Check authentication status
     */
    async checkAuthStatus() {
        try {
            const response = await this.sendMessage({ action: 'getAuthStatus' });
            
            if (response.success && response.data.isAuthenticated) {
                await this.showDashboard();
            } else {
                this.showAuthSection();
            }
            
        } catch (error) {
            console.error('Error checking auth status:', error);
            this.showAuthSection();
        }
    }

    /**
     * Handle authentication
     */
    async handleAuthentication() {
        try {
            Utils.showLoading('loadingOverlay');
            Utils.hideError();

            console.log('Starting authentication from popup...');
            const response = await this.sendMessage({ action: 'authenticate' });
            console.log('Authentication response:', response);

            if (response && response.success) {
                console.log('Authentication successful, showing dashboard...');
                await this.showDashboard();
                Utils.logEvent('user_authenticated');
            } else {
                const errorMsg = response?.error || 'Authentication failed';
                console.error('Authentication failed:', errorMsg);
                Utils.showError(`Authentication failed: ${errorMsg}`);
            }

        } catch (error) {
            console.error('Authentication error:', error);
            Utils.showError(`Authentication error: ${error.message}`);
        } finally {
            Utils.hideLoading('loadingOverlay');
        }
    }

    /**
     * Show authentication section
     */
    showAuthSection() {
        const authSection = document.getElementById('authSection');
        const dashboard = document.getElementById('dashboard');
        
        if (authSection) authSection.classList.remove('hidden');
        if (dashboard) dashboard.classList.add('hidden');
    }

    /**
     * Show main dashboard
     */
    async showDashboard() {
        try {
            const authSection = document.getElementById('authSection');
            const dashboard = document.getElementById('dashboard');
            
            if (authSection) authSection.classList.add('hidden');
            if (dashboard) dashboard.classList.remove('hidden');
            
            // Load channel info and analytics data
            await this.loadChannelInfo();
            await this.loadAnalyticsData();
            
        } catch (error) {
            console.error('Error showing dashboard:', error);
            Utils.showError('Failed to load dashboard data.');
        }
    }

    /**
     * Load channel information
     */
    async loadChannelInfo() {
        try {
            const response = await this.sendMessage({ action: 'getChannelInfo' });
            
            if (response.success && response.data) {
                this.displayChannelInfo(response.data);
            }
            
        } catch (error) {
            console.error('Error loading channel info:', error);
        }
    }

    /**
     * Display channel information
     */
    displayChannelInfo(channelData) {
        const channelAvatar = document.getElementById('channelAvatar');
        const channelName = document.getElementById('channelName');
        const channelStats = document.getElementById('channelStats');
        
        if (channelAvatar && channelData.thumbnail) {
            channelAvatar.src = channelData.thumbnail;
            channelAvatar.alt = channelData.title;
        }
        
        if (channelName) {
            channelName.textContent = channelData.title || 'Unknown Channel';
        }
        
        if (channelStats) {
            const subscribers = Utils.formatNumber(channelData.subscriberCount || 0);
            const videos = Utils.formatNumber(channelData.videoCount || 0);
            channelStats.textContent = `${subscribers} subscribers • ${videos} videos`;
        }
    }

    /**
     * Load analytics data
     */
    async loadAnalyticsData() {
        try {
            this.isLoading = true;
            this.showLoadingStates();
            
            const response = await this.sendMessage({ action: 'getAnalyticsData', days: 30 });
            
            if (response.success && response.data) {
                this.currentData = response.data;
                this.displayAnalyticsData(response.data);
            } else {
                throw new Error('Failed to load analytics data');
            }
            
        } catch (error) {
            console.error('Error loading analytics data:', error);
            Utils.showError('Failed to load analytics data. Please try again.');
        } finally {
            this.isLoading = false;
            this.hideLoadingStates();
        }
    }

    /**
     * Display analytics data
     */
    displayAnalyticsData(data) {
        this.displayBestTimes(data.bestTimes || []);
        this.displayHeatmap(data.heatmapData || []);
        this.displayQuickStats(data);
    }

    /**
     * Display best posting times
     */
    displayBestTimes(bestTimes) {
        const container = document.getElementById('bestTimes');
        if (!container) return;
        
        if (bestTimes.length === 0) {
            container.innerHTML = '<div class="loading">No data available yet. Post more videos to get insights!</div>';
            return;
        }
        
        const timesHtml = bestTimes.map(time => `
            <div class="time-slot">
                <div class="time-info">
                    <div class="time-day">${time.dayName}</div>
                    <div class="time-hour">${time.timeFormatted}</div>
                </div>
                <div class="engagement-score">${time.engagementScore}%</div>
            </div>
        `).join('');
        
        container.innerHTML = timesHtml;
    }

    /**
     * Display audience activity heatmap
     */
    displayHeatmap(heatmapData) {
        const container = document.getElementById('heatmap');
        if (!container) return;
        
        if (heatmapData.length === 0) {
            container.innerHTML = '<div class="loading">No heatmap data available</div>';
            return;
        }
        
        // Clear existing content
        container.innerHTML = '';
        
        // Create heatmap cells
        heatmapData.forEach(cell => {
            const cellElement = document.createElement('div');
            cellElement.className = 'heatmap-cell';
            cellElement.style.backgroundColor = cell.color;
            cellElement.title = cell.tooltip;
            cellElement.dataset.hour = cell.hour;
            
            // Add click handler for detailed info
            cellElement.addEventListener('click', () => {
                this.showHeatmapDetails(cell);
            });
            
            container.appendChild(cellElement);
        });
        
        // Add legend
        this.addHeatmapLegend(container);
    }

    /**
     * Add heatmap legend
     */
    addHeatmapLegend(container) {
        const legend = document.createElement('div');
        legend.className = 'heatmap-legend';
        legend.innerHTML = `
            <span>Low Activity</span>
            <div style="display: flex; gap: 2px;">
                <div style="width: 12px; height: 12px; background: #ebedf0; border-radius: 2px;"></div>
                <div style="width: 12px; height: 12px; background: #c6e48b; border-radius: 2px;"></div>
                <div style="width: 12px; height: 12px; background: #7bc96f; border-radius: 2px;"></div>
                <div style="width: 12px; height: 12px; background: #239a3b; border-radius: 2px;"></div>
                <div style="width: 12px; height: 12px; background: #196127; border-radius: 2px;"></div>
            </div>
            <span>High Activity</span>
        `;
        
        container.parentNode.appendChild(legend);
    }

    /**
     * Display quick statistics
     */
    displayQuickStats(data) {
        const peakDay = document.getElementById('peakDay');
        const peakHour = document.getElementById('peakHour');
        const avgViews = document.getElementById('avgViews');
        
        if (peakDay) {
            peakDay.textContent = Utils.getDayName(data.peakDay || 0);
        }
        
        if (peakHour) {
            peakHour.textContent = Utils.formatTime(data.peakHour || 0);
        }
        
        if (avgViews) {
            const avgViewsValue = data.totalViews && data.bestTimes ? 
                Math.round(data.totalViews / data.bestTimes.length) : 0;
            avgViews.textContent = Utils.formatNumber(avgViewsValue);
        }
    }

    /**
     * Show heatmap cell details
     */
    showHeatmapDetails(cellData) {
        // Create a simple tooltip or modal with detailed information
        const tooltip = document.createElement('div');
        tooltip.className = 'heatmap-tooltip';
        tooltip.innerHTML = `
            <strong>${Utils.formatTime(cellData.hour)}</strong><br>
            Engagement: ${Math.round(cellData.intensity * 100)}%<br>
            <small>Click outside to close</small>
        `;
        
        // Position and show tooltip
        tooltip.style.position = 'fixed';
        tooltip.style.background = 'rgba(0, 0, 0, 0.8)';
        tooltip.style.color = 'white';
        tooltip.style.padding = '10px';
        tooltip.style.borderRadius = '6px';
        tooltip.style.fontSize = '12px';
        tooltip.style.zIndex = '10000';
        tooltip.style.pointerEvents = 'none';
        
        document.body.appendChild(tooltip);
        
        // Remove tooltip after 3 seconds
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 3000);
    }

    /**
     * Refresh data
     */
    async refreshData() {
        try {
            Utils.hideError();
            await this.loadAnalyticsData();
            Utils.logEvent('data_refreshed');
        } catch (error) {
            console.error('Error refreshing data:', error);
            Utils.showError('Failed to refresh data. Please try again.');
        }
    }

    /**
     * Load settings
     */
    async loadSettings() {
        try {
            const response = await this.sendMessage({ action: 'getSettings' });
            
            if (response.success && response.data) {
                this.settings = response.data;
                this.applySettings(response.data);
            }
            
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    /**
     * Apply settings to UI
     */
    applySettings(settings) {
        const timezoneSelect = document.getElementById('timezone');
        const notificationsCheckbox = document.getElementById('notifications');
        
        if (timezoneSelect && settings.timezone) {
            timezoneSelect.value = settings.timezone;
        }
        
        if (notificationsCheckbox && typeof settings.notifications === 'boolean') {
            notificationsCheckbox.checked = settings.notifications;
        }
    }

    /**
     * Update timezone setting
     */
    async updateTimezone(timezone) {
        try {
            const updatedSettings = { ...this.settings, timezone };
            await this.sendMessage({ action: 'updateSettings', settings: updatedSettings });
            this.settings = updatedSettings;
        } catch (error) {
            console.error('Error updating timezone:', error);
        }
    }

    /**
     * Update notifications setting
     */
    async updateNotifications(enabled) {
        try {
            const updatedSettings = { ...this.settings, notifications: enabled };
            await this.sendMessage({ action: 'updateSettings', settings: updatedSettings });
            this.settings = updatedSettings;
        } catch (error) {
            console.error('Error updating notifications:', error);
        }
    }

    /**
     * Show loading states
     */
    showLoadingStates() {
        const elements = ['bestTimes', 'heatmap'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '<div class="loading">Loading...</div>';
            }
        });
    }

    /**
     * Hide loading states
     */
    hideLoadingStates() {
        // Loading states are replaced by actual content in display methods
    }

    /**
     * Retry last action
     */
    async retryLastAction() {
        Utils.hideError();
        await this.loadAnalyticsData();
    }

    /**
     * Send message to background script
     */
    async sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
