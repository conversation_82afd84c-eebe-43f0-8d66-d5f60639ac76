/**
 * Background service worker for YouTube Studio Analytics Extension
 */

// Import utility functions (Note: In Manifest V3, we need to use importScripts)
importScripts('utils.js', 'auth.js', 'analytics.js');

class BackgroundService {
    constructor() {
        this.notificationScheduler = null;
        this.dataRefreshInterval = null;
        this.init();
    }

    /**
     * Initialize background service
     */
    async init() {
        console.log('Background service initialized');
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize authentication
        await this.initializeAuth();
        
        // Set up periodic data refresh
        this.setupDataRefresh();
        
        // Set up notification scheduler
        this.setupNotificationScheduler();
    }

    /**
     * Set up Chrome extension event listeners
     */
    setupEventListeners() {
        try {
            // Handle extension installation
            if (chrome.runtime && chrome.runtime.onInstalled) {
                chrome.runtime.onInstalled.addListener((details) => {
                    this.handleInstallation(details);
                });
            }

            // Handle messages from popup
            if (chrome.runtime && chrome.runtime.onMessage) {
                chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                    this.handleMessage(message, sender, sendResponse);
                    return true; // Keep message channel open for async response
                });
            }

            // Handle notification clicks
            if (chrome.notifications && chrome.notifications.onClicked) {
                chrome.notifications.onClicked.addListener((notificationId) => {
                    this.handleNotificationClick(notificationId);
                });
            }

            // Handle alarm events for scheduled tasks
            if (chrome.alarms && chrome.alarms.onAlarm) {
                chrome.alarms.onAlarm.addListener((alarm) => {
                    this.handleAlarm(alarm);
                });
            }

            // Handle storage changes
            if (chrome.storage && chrome.storage.onChanged) {
                chrome.storage.onChanged.addListener((changes, namespace) => {
                    this.handleStorageChange(changes, namespace);
                });
            }
        } catch (error) {
            console.error('Error setting up event listeners:', error);
        }
    }

    /**
     * Handle extension installation
     */
    async handleInstallation(details) {
        console.log('Extension installed:', details.reason);
        
        if (details.reason === 'install') {
            // First time installation
            await this.setDefaultSettings();
            Utils.logEvent('extension_installed');
        } else if (details.reason === 'update') {
            // Extension updated
            Utils.logEvent('extension_updated', { version: chrome.runtime.getManifest().version });
        }
    }

    /**
     * Set default settings on first installation
     */
    async setDefaultSettings() {
        const defaultSettings = {
            notifications: true,
            timezone: 'auto',
            refreshInterval: 30, // minutes
            dataRetention: 30 // days
        };
        
        await Utils.storeData('settings', defaultSettings);
    }

    /**
     * Handle messages from popup and content scripts
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            console.log('Background received message:', message);
            
            switch (message.action) {
                case 'authenticate':
                    const authResult = await authManager.authenticate();
                    sendResponse({ success: authResult });
                    break;
                    
                case 'getAuthStatus':
                    const authStatus = authManager.getAuthStatus();
                    sendResponse({ success: true, data: authStatus });
                    break;
                    
                case 'getChannelInfo':
                    const channelInfo = await youtubeAnalytics.getChannelInfo();
                    sendResponse({ success: true, data: channelInfo });
                    break;
                    
                case 'getAnalyticsData':
                    const analyticsData = await youtubeAnalytics.getAudienceRetentionData(message.days || 30);
                    sendResponse({ success: true, data: analyticsData });
                    break;
                    
                case 'refreshData':
                    await youtubeAnalytics.clearCache();
                    const refreshedData = await youtubeAnalytics.getAudienceRetentionData();
                    sendResponse({ success: true, data: refreshedData });
                    break;
                    
                case 'signOut':
                    await authManager.signOut();
                    await youtubeAnalytics.clearCache();
                    sendResponse({ success: true });
                    break;
                    
                case 'updateSettings':
                    await Utils.storeData('settings', message.settings);
                    this.applySettings(message.settings);
                    sendResponse({ success: true });
                    break;
                    
                case 'getSettings':
                    const settings = await Utils.getData('settings');
                    sendResponse({ success: true, data: settings });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
            
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ 
                success: false, 
                error: Utils.handleApiError(error) 
            });
        }
    }

    /**
     * Initialize authentication on startup
     */
    async initializeAuth() {
        try {
            const isAuthenticated = await authManager.init();
            if (isAuthenticated) {
                console.log('User is authenticated');
                // Load cached analytics data
                await youtubeAnalytics.loadCachedData();
            }
        } catch (error) {
            console.error('Error initializing auth:', error);
        }
    }

    /**
     * Set up periodic data refresh
     */
    setupDataRefresh() {
        try {
            if (chrome.alarms && chrome.alarms.create) {
                // Create alarm for periodic data refresh
                chrome.alarms.create('dataRefresh', {
                    delayInMinutes: 30,
                    periodInMinutes: 30
                });
                console.log('Data refresh alarm created');
            } else {
                console.warn('Chrome alarms API not available');
            }
        } catch (error) {
            console.error('Error setting up data refresh:', error);
        }
    }

    /**
     * Set up notification scheduler
     */
    setupNotificationScheduler() {
        try {
            if (chrome.alarms && chrome.alarms.create) {
                // Create alarm for checking optimal posting times
                chrome.alarms.create('checkPostingTimes', {
                    delayInMinutes: 60,
                    periodInMinutes: 60
                });
                console.log('Notification scheduler alarm created');
            } else {
                console.warn('Chrome alarms API not available');
            }
        } catch (error) {
            console.error('Error setting up notification scheduler:', error);
        }
    }

    /**
     * Handle alarm events
     */
    async handleAlarm(alarm) {
        console.log('Alarm triggered:', alarm.name);
        
        switch (alarm.name) {
            case 'dataRefresh':
                await this.refreshAnalyticsData();
                break;
                
            case 'checkPostingTimes':
                await this.checkOptimalPostingTimes();
                break;
        }
    }

    /**
     * Refresh analytics data in background
     */
    async refreshAnalyticsData() {
        try {
            if (!authManager.isAuthenticated) {
                return;
            }
            
            console.log('Refreshing analytics data in background');
            await youtubeAnalytics.clearCache();
            await youtubeAnalytics.getAudienceRetentionData();
            
            Utils.logEvent('background_data_refresh');
            
        } catch (error) {
            console.error('Error refreshing analytics data:', error);
        }
    }

    /**
     * Check if current time is optimal for posting
     */
    async checkOptimalPostingTimes() {
        try {
            const settings = await Utils.getData('settings');
            if (!settings || !settings.notifications) {
                return;
            }
            
            if (!authManager.isAuthenticated) {
                return;
            }
            
            const analyticsData = await youtubeAnalytics.getAudienceRetentionData();
            if (!analyticsData || !analyticsData.bestTimes) {
                return;
            }
            
            const now = new Date();
            const currentHour = now.getHours();
            const currentDay = now.getDay();
            
            // Check if current time matches any of the best posting times
            const matchingTime = analyticsData.bestTimes.find(time => 
                time.day === currentDay && 
                Math.abs(time.hour - currentHour) <= 1 // Within 1 hour
            );
            
            if (matchingTime) {
                await this.sendOptimalTimeNotification(matchingTime);
            }
            
        } catch (error) {
            console.error('Error checking optimal posting times:', error);
        }
    }

    /**
     * Send notification for optimal posting time
     */
    async sendOptimalTimeNotification(timeData) {
        try {
            const title = '🎯 Optimal Posting Time!';
            const message = `Now is a great time to post! ${timeData.dayName} at ${timeData.timeFormatted} has ${timeData.engagementScore}% engagement score.`;
            
            await chrome.notifications.create('optimalTime', {
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message,
                buttons: [
                    { title: 'Open YouTube Studio' },
                    { title: 'Dismiss' }
                ]
            });
            
            Utils.logEvent('optimal_time_notification_sent', timeData);
            
        } catch (error) {
            console.error('Error sending notification:', error);
        }
    }

    /**
     * Handle notification clicks
     */
    handleNotificationClick(notificationId) {
        console.log('Notification clicked:', notificationId);
        
        if (notificationId === 'optimalTime') {
            // Open YouTube Studio in new tab
            chrome.tabs.create({
                url: 'https://studio.youtube.com'
            });
        }
        
        // Clear the notification
        chrome.notifications.clear(notificationId);
    }

    /**
     * Handle storage changes
     */
    handleStorageChange(changes, namespace) {
        console.log('Storage changed:', changes, namespace);
        
        if (changes.settings) {
            this.applySettings(changes.settings.newValue);
        }
    }

    /**
     * Apply settings changes
     */
    applySettings(settings) {
        if (!settings) return;

        try {
            // Update notification scheduler based on settings
            if (settings.notifications) {
                this.setupNotificationScheduler();
            } else if (chrome.alarms && chrome.alarms.clear) {
                chrome.alarms.clear('checkPostingTimes');
            }

            // Update data refresh interval
            if (settings.refreshInterval && chrome.alarms) {
                if (chrome.alarms.clear) {
                    chrome.alarms.clear('dataRefresh');
                }
                if (chrome.alarms.create) {
                    chrome.alarms.create('dataRefresh', {
                        delayInMinutes: settings.refreshInterval,
                        periodInMinutes: settings.refreshInterval
                    });
                }
            }
        } catch (error) {
            console.error('Error applying settings:', error);
        }
    }

    /**
     * Clean up old data based on retention settings
     */
    async cleanupOldData() {
        try {
            const settings = await Utils.getData('settings');
            const retentionDays = settings?.dataRetention || 30;
            
            // This would implement cleanup logic for old cached data
            // For now, we'll just clear the cache if it's older than retention period
            const cached = await Utils.getData('analyticsCache');
            if (cached && cached.expiry) {
                const ageInDays = (Date.now() - cached.expiry) / (1000 * 60 * 60 * 24);
                if (ageInDays > retentionDays) {
                    await Utils.clearData('analyticsCache');
                }
            }
            
        } catch (error) {
            console.error('Error cleaning up old data:', error);
        }
    }
}

// Initialize background service
const backgroundService = new BackgroundService();
